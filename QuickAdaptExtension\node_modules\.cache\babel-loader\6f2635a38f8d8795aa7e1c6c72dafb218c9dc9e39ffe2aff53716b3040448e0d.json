{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\RTE\\\\RTESection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useRef, memo, useMemo, useCallback } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  items: {\n    id,\n    rteBoxValue\n  },\n  boxRef,\n  handleFocus,\n  handleeBlur,\n  isPopoverOpen,\n  setIsPopoverOpen,\n  currentRTEFocusedId\n}, ref) => {\n  var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6, _toolTipGuideMetaData7, _toolTipGuideMetaData8, _toolTipGuideMetaData9, _toolTipGuideMetaData10;\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setIsUnSavedChanges,\n    setHtmlContent,\n    textvaluess,\n    setTextvaluess,\n    backgroundC,\n    setBackgroundC,\n    Bbordercolor,\n    BborderSize,\n    bpadding,\n    sectionColor,\n    setSectionColor,\n    handleTooltipRTEBlur,\n    handleTooltipRTEValue,\n    handleRTEDeleteSection,\n    handleRTECloneSection,\n    tooltip,\n    currentStep,\n    toolTipGuideMetaData\n  } = useDrawerStore(state => state);\n  // Removed unused state variables since we're using Jodit editor directly\n\n  const [isEditing, setIsEditing] = useState(false);\n  const [toolbarVisible, setToolbarVisible] = useState(false);\n  const editorRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // State to track content for dynamic icon positioning\n  const [contentState, setContentState] = useState({\n    isEmpty: true,\n    isScrollable: false\n  });\n\n  // Memoize Jodit config to prevent re-renders and focus loss\n  const joditConfig = useMemo(() => ({\n    readonly: false,\n    // Hide main toolbar by default, controlled by toolbarVisible state\n    toolbar: toolbarVisible,\n    // Enable inline toolbar for text selection\n    // toolbarInline: true,\n    // toolbarInlineForSelection: true,\n    // toolbarInlineDisabledButtons: ['source', 'fullsize'],\n    // toolbarInlineDisableFor: [],\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    // Inline toolbar width configuration\n    toolbarButtonSize: 'small',\n    toolbarInlineWidth: 500,\n    toolbarInlineMaxWidth: 600,\n    toolbarInlineMinWidth: 450,\n    // Additional popup configuration for inline toolbar\n    popup: {\n      selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\n      toolbar: {\n        width: 500,\n        maxWidth: 600,\n        minWidth: 450\n      }\n    },\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    statusbar: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: false,\n    // Enable auto-resize behavior\n    height: 'auto',\n    minHeight: toolbarVisible ? 130 : 28,\n    // 130px when toolbar visible, 28px when hidden\n    maxHeight: 150,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Ensure dialogs appear in correct position\n    zIndex: 100000,\n    globalFullSize: false,\n    // Add custom CSS to ensure text is visible\n    style: {\n      color: '#000000 !important',\n      backgroundColor: '#ffffff',\n      fontFamily: 'Poppins, sans-serif'\n    },\n    // Override editor styles to ensure text visibility\n    editorCssClass: 'jodit-tooltip-editor',\n    // Set default content styling\n    enter: 'p',\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [id, toolbarVisible]);\n\n  // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\n  const isContentEmpty = content => {\n    if (!content) return true;\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    return textContent.length === 0;\n  };\n\n  // Helper function to check if content is scrollable\n  const isContentScrollable = () => {\n    if (containerRef !== null && containerRef !== void 0 && containerRef.current) {\n      const workplace = containerRef.current.querySelector('.jodit-workplace');\n      if (workplace) {\n        return workplace.scrollHeight > workplace.clientHeight;\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = content => {\n    const isEmpty = isContentEmpty(content);\n    const isScrollable = isContentScrollable();\n    setContentState({\n      isEmpty,\n      isScrollable\n    });\n  };\n\n  // Toggle toolbar function\n  const toggleToolbar = () => {\n    setToolbarVisible(!toolbarVisible);\n  };\n\n  // Dynamic CSS based on toolbar visibility\n  const dynamicCSS = `\n\t\t\t/* Hide the add new line button */\n\t\t\t.jodit-add-new-line {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\n\t\t\t.jodit-wysiwyg {\n\t\t\t\tcolor: #000000 !important;\n\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\tline-height: 1.4 !important;\n\t\t\t\tpadding: 8px !important;\n\t\t\t}\n\t\t\t/* Height and scrolling behavior for tooltip RTE */\n\t\t\t.jodit-workplace {\n\t\t\t\tmin-height: ${toolbarVisible ? '130px' : '28px'} !important;\n\t\t\t\tmax-height: 150px !important;\n\t\t\t\toverflow-y: auto !important;\n\t\t\t\tline-height: 1.4 !important;\n\t\t\t}\n\t\t\t.jodit-container {\n\t\t\t\tborder: none !important;\n\t\t\t}\n\t\t\t/* Target the specific jodit container class combination */\n\t\t\t.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {\n\t\t\t\tborder: 0 !important;\n\t\t\t}\n\t\t\t.jodit-wysiwyg p {\n\t\t\t\tcolor: #000000 !important;\n\t\t\t\tmargin: 0 0 4px 0 !important;\n\t\t\t\tpadding: 0 !important;\n\t\t\t\tline-height: 1.4 !important;\n\t\t\t}\n\t\t\t/* Enhanced scrollbar styling for tooltip RTE */\n\t\t\t.jodit-workplace::-webkit-scrollbar {\n\t\t\t\twidth: 6px !important;\n\t\t\t}\n\t\t\t.jodit-workplace::-webkit-scrollbar-track {\n\t\t\t\tbackground: #f1f1f1 !important;\n\t\t\t\tborder-radius: 3px !important;\n\t\t\t}\n\t\t\t.jodit-workplace::-webkit-scrollbar-thumb {\n\t\t\t\tbackground: #c1c1c1 !important;\n\t\t\t\tborder-radius: 3px !important;\n\t\t\t}\n\t\t\t.jodit-workplace::-webkit-scrollbar-thumb:hover {\n\t\t\t\tbackground: #a8a8a8 !important;\n\t\t\t}\n\t\t\t/* Ensure text is visible in all states */\n\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\n\t\t\t\tcolor: #000000 !important;\n\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\tline-height: 1.4 !important;\n\t\t\t\tpadding: 8px !important;\n\t\t\t}\n\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\n\t\t\t\tcolor: #000000 !important;\n\t\t\t}\n\t\t`;\n\n  // Memoize onChange handler to prevent re-renders\n  const handleContentChange = useCallback(newContent => {\n    handleTooltipRTEValue(id, newContent);\n    // Update content state for dynamic icon positioning\n    updateContentState(newContent);\n  }, [id, handleTooltipRTEValue, updateContentState]);\n\n  // Initialize content state on mount\n  useEffect(() => {\n    updateContentState(rteBoxValue || \"\");\n  }, [rteBoxValue]);\n\n  // const handleInput = () => {\n  // \t// Update the content state when user types\n  // \tif (boxRef.current) {\n  // \t\tconst updatedContent = boxRef.current.innerHTML;\n  // \t\tsetContent(updatedContent); // Store the content in state\n  // \t\tsetHtmlContent(updatedContent); // Update the HTML content\n  // \t\tsetIsUnSavedChanges(true);\n  // \t\tpreserveCaretPosition();\n  // \t}\n  // };\n  // Removed caret position functions since we're using Jodit editor\n\n  // useEffect(() => {\n  // \t// After content update, restore the cursor position\n  // \trestoreCaretPosition();\n  // }, [boxRef.current?.innerHTML]); // Run when content changes\n\n  // Remove section\n\n  // useEffect(() => {\n  // \tif (boxRef.current?.innerHTML?.trim()) {\n  // \t\tsetIsUnSavedChanges(true);\n  // \t}\n  // }, [boxRef.current?.innerHTML?.trim()]);\n\n  // Removed useEffect since we're using Jodit editor directly\n\n  // Auto-focus the editor when editing mode is activated\n  useEffect(() => {\n    if (isEditing && editorRef.current) {\n      setTimeout(() => {\n        editorRef.current.editor.focus();\n      }, 50);\n    }\n  }, [isEditing]);\n\n  // Handle clicks outside the editor to close editing mode\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Check if the target is inside the editor or related elements\n      if (containerRef.current && !containerRef.current.contains(event.target) &&\n      // Click outside the editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setIsEditing(false); // Close the editor if clicked outside\n        setToolbarVisible(false); // Also hide toolbar when clicking outside\n      }\n    };\n    if (isEditing) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        position: \"relative\",\n        //padding: 0,\n        margin: 0,\n        boxSizing: \"border-box\",\n        transition: \"border 0.2s ease-in-out\",\n        backgroundColor: sectionColor || \"defaultColor\"\n        //border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\n        // padding: `${bpadding}px !important` || \"0\",\n      },\n      className: \"qadpt-rte\",\n      id: \"rte-box\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: \"100%\",\n          position: \"relative\",\n          color: \"#000000\",\n          backgroundColor: \"#ffffff\"\n        },\n        className: \"rte-container\",\n        onMouseEnter: () => {\n          // Show action icons on hover - wait for workplace to be available\n          setTimeout(() => {\n            const workplace = document.querySelector(`[data-rte-id=\"${id}\"] .jodit-workplace`);\n            if (workplace) {\n              const actionIcons = workplace.querySelectorAll('.rte-action-icon');\n              actionIcons.forEach(icon => {\n                icon.style.opacity = '1';\n              });\n            }\n          }, 100);\n        },\n        onMouseLeave: () => {\n          // Hide action icons when not hovering\n          const workplace = document.querySelector(`[data-rte-id=\"${id}\"] .jodit-workplace`);\n          if (workplace) {\n            const actionIcons = workplace.querySelectorAll('.rte-action-icon');\n            actionIcons.forEach(icon => {\n              icon.style.opacity = '0';\n            });\n          }\n        },\n        \"data-rte-id\": id,\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => handleRTECloneSection(id),\n          disabled: ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.length) >= 3,\n          title: ((_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.length) >= 3 ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n          className: \"rte-action-icon\",\n          sx: {\n            position: \"absolute\",\n            top: \"8px\",\n            right: \"15px\",\n            zIndex: 1002,\n            opacity: 0,\n            transition: \"opacity 0.2s ease-in-out\",\n            width: \"28px\",\n            height: \"28px\",\n            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n            \"&:hover\": {\n              backgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\"\n            },\n            svg: {\n              height: \"16px\",\n              width: \"16px\",\n              path: {\n                fill: \"var(--primarycolor)\"\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: copyicon\n            },\n            style: {\n              opacity: ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.length) >= 3 ? 0.5 : 1,\n              height: '16px',\n              width: '16px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => handleRTEDeleteSection(id),\n          disabled: ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : (_toolTipGuideMetaData8 = _toolTipGuideMetaData7.containers) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.length) === 1,\n          title: translate(\"Delete Section\"),\n          className: \"rte-action-icon\",\n          sx: {\n            position: \"absolute\",\n            top: \"8px\",\n            right: \"47px\",\n            // 15px + 28px (clone width) + 4px spacing\n            zIndex: 1002,\n            opacity: 0,\n            transition: \"opacity 0.2s ease-in-out\",\n            width: \"28px\",\n            height: \"28px\",\n            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n            \"&:hover\": {\n              backgroundColor: \"rgba(255, 0, 0, 0.1) !important\"\n            },\n            svg: {\n              height: \"16px\",\n              width: \"16px\",\n              path: {\n                fill: \"var(--primarycolor)\"\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: deleteicon\n            },\n            style: {\n              opacity: ((_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData9 === void 0 ? void 0 : (_toolTipGuideMetaData10 = _toolTipGuideMetaData9.containers) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.length) === 1 ? 0.5 : 1,\n              height: '16px',\n              width: '16px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: dynamicCSS\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n          ref: editorRef,\n          value: rteBoxValue || \"\",\n          config: joditConfig,\n          onChange: handleContentChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: e => {\n            e.stopPropagation();\n            toggleToolbar();\n          },\n          sx: {\n            position: \"absolute\",\n            // Dynamic positioning: when toolbar is visible, always bottom-right\n            bottom: toolbarVisible ? \"8px\" : contentState.isEmpty ? \"50%\" : contentState.isScrollable ? \"8px\" : \"2px\",\n            right: toolbarVisible ? \"2px\" : contentState.isEmpty ? \"auto\" : \"2px\",\n            left: toolbarVisible ? \"auto\" : contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\",\n            transform: toolbarVisible ? \"none\" : contentState.isEmpty ? \"translateY(50%)\" : \"none\",\n            width: \"24px\",\n            height: \"24px\",\n            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n            zIndex: contentState.isScrollable ? 1001 : 1000,\n            // Higher z-index when scrollable to stay on top\n            \"&:hover\": {\n              backgroundColor: \"rgba(255, 255, 255, 1)\"\n            },\n            \"& svg\": {\n              width: \"16px\",\n              height: \"16px\"\n            }\n          },\n          title: translate(\"Toggle Toolbar\"),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: editicon\n            },\n            style: {\n              height: '16px',\n              width: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}, \"ku1j8Tc0TSsnux0bVoKP293u4U4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"ku1j8Tc0TSsnux0bVoKP293u4U4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default _c3 = /*#__PURE__*/memo(RTEsection);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useRef", "memo", "useMemo", "useCallback", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "items", "id", "rteBoxValue", "boxRef", "handleFocus", "handleeBlur", "isPopoverOpen", "setIsPopoverOpen", "currentRTEFocusedId", "ref", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "t", "translate", "setIsUnSavedChanges", "setHtmlContent", "textvaluess", "setTextvaluess", "backgroundC", "setBackgroundC", "Bbordercolor", "BborderSize", "bpadding", "sectionColor", "setSectionColor", "handleTooltipRTEBlur", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "currentStep", "toolTipGuideMetaData", "state", "isEditing", "setIsEditing", "toolbarVisible", "setToolbarVisible", "editor<PERSON><PERSON>", "containerRef", "contentState", "setContentState", "isEmpty", "isScrollable", "joditConfig", "readonly", "toolbar", "toolbarSticky", "toolbarAdaptive", "toolbarButtonSize", "toolbarInlineWidth", "toolbarInlineMaxWidth", "toolbarInlineMinWidth", "popup", "selection", "width", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "buttons", "name", "iconURL", "list", "autofocus", "height", "minHeight", "maxHeight", "popupRoot", "document", "body", "zIndex", "globalFullSize", "style", "color", "backgroundColor", "fontFamily", "editor<PERSON>s<PERSON><PERSON>", "enter", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "controls", "font", "isContentEmpty", "content", "textContent", "replace", "trim", "length", "isContentScrollable", "current", "workplace", "querySelector", "scrollHeight", "clientHeight", "updateContentState", "toggleToolbar", "dynamicCSS", "handleContentChange", "newContent", "setTimeout", "editor", "focus", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "addEventListener", "removeEventListener", "children", "sx", "display", "alignItems", "position", "margin", "boxSizing", "transition", "className", "onMouseEnter", "actionIcons", "querySelectorAll", "for<PERSON>ach", "icon", "opacity", "onMouseLeave", "size", "onClick", "disabled", "containers", "title", "top", "right", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "justifyContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "config", "onChange", "e", "stopPropagation", "bottom", "left", "transform", "_c2", "_c3", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/RTE/RTESection.tsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from \"react\";\r\nimport { Box, Popover, Typography, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\n\r\nimport RTE from \"./RTE\";\r\nimport useDrawerStore, { IRTEContainer, TSectionType } from \"../../../../store/drawerStore\";\r\nimport { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from \"@mui/icons-material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n\titems: IRTEContainer;\r\n\tboxRef: React.RefObject<HTMLDivElement>;\r\n\thandleFocus: (id: string) => void;\r\n\thandleeBlur: (id: string) => void;\r\n\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tcurrentRTEFocusedId: string;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n\t(\r\n\t\t{\r\n\t\t\titems: { id, rteBoxValue },\r\n\t\t\tboxRef,\r\n\t\t\thandleFocus,\r\n\t\t\thandleeBlur,\r\n\r\n\t\t\tisPopoverOpen,\r\n\t\t\tsetIsPopoverOpen,\r\n\t\t\tcurrentRTEFocusedId,\r\n\t\t},\r\n\t\tref\r\n\t) => {\r\n\t\tconst { t: translate } = useTranslation();\r\n\t\tconst {\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tsetHtmlContent,\r\n\t\t\ttextvaluess,\r\n\t\t\tsetTextvaluess,\r\n\t\t\tbackgroundC,\r\n\t\t\tsetBackgroundC,\r\n\t\t\tBbordercolor,\r\n\t\t\tBborderSize,\r\n\t\t\tbpadding,\r\n\t\t\tsectionColor,\r\n\t\t\tsetSectionColor,\r\n\t\t\thandleTooltipRTEBlur,\r\n\t\t\thandleTooltipRTEValue,\r\n\t\t\thandleRTEDeleteSection,\r\n\t\t\thandleRTECloneSection,\r\n\t\t\ttooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t} = useDrawerStore((state) => state);\r\n\t\t// Removed unused state variables since we're using Jodit editor directly\r\n\r\n\t\tconst [isEditing, setIsEditing] = useState(false);\r\n\t\tconst [toolbarVisible, setToolbarVisible] = useState(false);\r\n\t\tconst editorRef = useRef(null);\r\n\t\tconst containerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t\t// State to track content for dynamic icon positioning\r\n\t\tconst [contentState, setContentState] = useState<{ isEmpty: boolean, isScrollable: boolean }>({ isEmpty: true, isScrollable: false });\r\n\r\n\t\t// Memoize Jodit config to prevent re-renders and focus loss\r\n\t\tconst joditConfig = useMemo((): any => ({\r\n\t\t\treadonly: false,\r\n\t\t\t// Hide main toolbar by default, controlled by toolbarVisible state\r\n\t\t\ttoolbar: toolbarVisible,\r\n\t\t\t// Enable inline toolbar for text selection\r\n\t\t\t// toolbarInline: true,\r\n\t\t\t// toolbarInlineForSelection: true,\r\n\t\t\t// toolbarInlineDisabledButtons: ['source', 'fullsize'],\r\n\t\t\t// toolbarInlineDisableFor: [],\r\n\t\t\ttoolbarSticky: false,\r\n\t\t\ttoolbarAdaptive: false,\r\n\t\t\t// Inline toolbar width configuration\r\n\t\t\ttoolbarButtonSize: 'small',\r\n\t\t\ttoolbarInlineWidth: 500,\r\n\t\t\ttoolbarInlineMaxWidth: 600,\r\n\t\t\ttoolbarInlineMinWidth: 450,\r\n\t\t\t// Additional popup configuration for inline toolbar\r\n\t\t\tpopup: {\r\n\t\t\t\tselection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\r\n\t\t\t\ttoolbar: {\r\n\t\t\t\t\twidth: 500,\r\n\t\t\t\t\tmaxWidth: 600,\r\n\t\t\t\t\tminWidth: 450\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowCharsCounter: false,\r\n\t\t\tshowWordsCounter: false,\r\n\t\t\tshowXPathInStatusbar: false,\r\n\t\t\tstatusbar: false,\r\n\t\t\tpastePlain: true,\r\n\t\t\taskBeforePasteHTML: false,\r\n\t\t\taskBeforePasteFromWord: false,\r\n\t\t\tbuttons: [\r\n\t\t\t\t'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n\t\t\t\t'font', 'fontsize', 'link',\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'more',\r\n\t\t\t\t\ticonURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n\t\t\t\t\tlist: [\r\n\t\t\t\t\t\t'image', 'video', 'table',\r\n\t\t\t\t\t\t'align', 'undo', 'redo', '|',\r\n\t\t\t\t\t\t'hr', 'eraser', 'copyformat',\r\n\t\t\t\t\t\t'symbol', 'print', 'superscript', 'subscript', '|',\r\n\t\t\t\t\t\t'outdent', 'indent', 'paragraph',\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tautofocus: false,\r\n\t\t\t// Enable auto-resize behavior\r\n\t\t\theight: 'auto',\r\n\t\t\tminHeight: toolbarVisible ? 130 : 28, // 130px when toolbar visible, 28px when hidden\r\n\t\t\tmaxHeight: 150,\r\n\t\t\t// Fix dialog positioning by setting popup root to document body\r\n\t\t\tpopupRoot: document.body,\r\n\t\t\t// Ensure dialogs appear in correct position\r\n\t\t\tzIndex: 100000,\r\n\t\t\tglobalFullSize: false,\r\n\t\t\t// Add custom CSS to ensure text is visible\r\n\t\t\tstyle: {\r\n\t\t\t\tcolor: '#000000 !important',\r\n\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\tfontFamily: 'Poppins, sans-serif',\r\n\t\t\t},\r\n\t\t\t// Override editor styles to ensure text visibility\r\n\t\t\teditorCssClass: 'jodit-tooltip-editor',\r\n\t\t\t// Set default content styling\r\n\t\t\tenter: 'p' as const,\r\n\t\t\t// Fix link dialog positioning\r\n\t\t\tlink: {\r\n\t\t\t\tfollowOnDblClick: false,\r\n\t\t\t\tprocessVideoLink: true,\r\n\t\t\t\tprocessPastedLink: true,\r\n\t\t\t\topenInNewTabCheckbox: true,\r\n\t\t\t\tnoFollowCheckbox: false,\r\n\t\t\t\tmodeClassName: 'input' as const,\r\n\t\t\t},\r\n\t\t\t// Dialog configuration\r\n\t\t\tdialog: {\r\n\t\t\t\tzIndex: 100001,\r\n\t\t\t},\r\n\t\t\tcontrols: {\r\n\t\t\t\tfont: {\r\n\t\t\t\t\tlist: {\r\n\t\t\t\t\t\t\"Poppins, sans-serif\": \"Poppins\",\r\n\t\t\t\t\t\t\"Roboto, sans-serif\": \"Roboto\",\r\n\t\t\t\t\t\t\"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n\t\t\t\t\t\t\"Open Sans, sans-serif\": \"Open Sans\",\r\n\t\t\t\t\t\t\"Calibri, sans-serif\": \"Calibri\",\r\n\t\t\t\t\t\t\"Century Gothic, sans-serif\": \"Century Gothic\",\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}), [id, toolbarVisible]);\r\n\r\n\t\t// Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\r\n\t\tconst isContentEmpty = (content: string): boolean => {\r\n\t\t\tif (!content) return true;\r\n\t\t\t// Remove HTML tags and check if there's actual text content\r\n\t\t\tconst textContent = content.replace(/<[^>]*>/g, '').trim();\r\n\t\t\treturn textContent.length === 0;\r\n\t\t};\r\n\r\n\t\t// Helper function to check if content is scrollable\r\n\t\tconst isContentScrollable = (): boolean => {\r\n\t\t\tif (containerRef?.current) {\r\n\t\t\t\tconst workplace = containerRef.current.querySelector('.jodit-workplace');\r\n\t\t\t\tif (workplace) {\r\n\t\t\t\t\treturn workplace.scrollHeight > workplace.clientHeight;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t};\r\n\r\n\t\t// Update content state for dynamic icon positioning\r\n\t\tconst updateContentState = (content: string) => {\r\n\t\t\tconst isEmpty = isContentEmpty(content);\r\n\t\t\tconst isScrollable = isContentScrollable();\r\n\r\n\t\t\tsetContentState({ isEmpty, isScrollable });\r\n\t\t};\r\n\r\n\t\t// Toggle toolbar function\r\n\t\tconst toggleToolbar = () => {\r\n\t\t\tsetToolbarVisible(!toolbarVisible);\r\n\t\t};\r\n\r\n\t\t// Dynamic CSS based on toolbar visibility\r\n\t\tconst dynamicCSS = `\r\n\t\t\t/* Hide the add new line button */\r\n\t\t\t.jodit-add-new-line {\r\n\t\t\t\tdisplay: none !important;\r\n\t\t\t}\r\n\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\r\n\t\t\t.jodit-wysiwyg {\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\tpadding: 8px !important;\r\n\t\t\t}\r\n\t\t\t/* Height and scrolling behavior for tooltip RTE */\r\n\t\t\t.jodit-workplace {\r\n\t\t\t\tmin-height: ${toolbarVisible ? '130px' : '28px'} !important;\r\n\t\t\t\tmax-height: 150px !important;\r\n\t\t\t\toverflow-y: auto !important;\r\n\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t}\r\n\t\t\t.jodit-container {\r\n\t\t\t\tborder: none !important;\r\n\t\t\t}\r\n\t\t\t/* Target the specific jodit container class combination */\r\n\t\t\t.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {\r\n\t\t\t\tborder: 0 !important;\r\n\t\t\t}\r\n\t\t\t.jodit-wysiwyg p {\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\tmargin: 0 0 4px 0 !important;\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t}\r\n\t\t\t/* Enhanced scrollbar styling for tooltip RTE */\r\n\t\t\t.jodit-workplace::-webkit-scrollbar {\r\n\t\t\t\twidth: 6px !important;\r\n\t\t\t}\r\n\t\t\t.jodit-workplace::-webkit-scrollbar-track {\r\n\t\t\t\tbackground: #f1f1f1 !important;\r\n\t\t\t\tborder-radius: 3px !important;\r\n\t\t\t}\r\n\t\t\t.jodit-workplace::-webkit-scrollbar-thumb {\r\n\t\t\t\tbackground: #c1c1c1 !important;\r\n\t\t\t\tborder-radius: 3px !important;\r\n\t\t\t}\r\n\t\t\t.jodit-workplace::-webkit-scrollbar-thumb:hover {\r\n\t\t\t\tbackground: #a8a8a8 !important;\r\n\t\t\t}\r\n\t\t\t/* Ensure text is visible in all states */\r\n\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\tpadding: 8px !important;\r\n\t\t\t}\r\n\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t}\r\n\t\t`;\r\n\r\n\t\t// Memoize onChange handler to prevent re-renders\r\n\t\tconst handleContentChange = useCallback((newContent: string) => {\r\n\t\t\thandleTooltipRTEValue(id, newContent);\r\n\t\t\t// Update content state for dynamic icon positioning\r\n\t\t\tupdateContentState(newContent);\r\n\t\t}, [id, handleTooltipRTEValue, updateContentState]);\r\n\r\n\t\t// Initialize content state on mount\r\n\t\tuseEffect(() => {\r\n\t\t\tupdateContentState(rteBoxValue || \"\");\r\n\t\t}, [rteBoxValue]);\r\n\r\n\t\t// const handleInput = () => {\r\n\t\t// \t// Update the content state when user types\r\n\t\t// \tif (boxRef.current) {\r\n\t\t// \t\tconst updatedContent = boxRef.current.innerHTML;\r\n\t\t// \t\tsetContent(updatedContent); // Store the content in state\r\n\t\t// \t\tsetHtmlContent(updatedContent); // Update the HTML content\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t\tpreserveCaretPosition();\r\n\t\t// \t}\r\n\t\t// };\r\n\t\t// Removed caret position functions since we're using Jodit editor\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \t// After content update, restore the cursor position\r\n\t\t// \trestoreCaretPosition();\r\n\t\t// }, [boxRef.current?.innerHTML]); // Run when content changes\r\n\r\n\t\t// Remove section\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \tif (boxRef.current?.innerHTML?.trim()) {\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t}\r\n\t\t// }, [boxRef.current?.innerHTML?.trim()]);\r\n\r\n\t\t// Removed useEffect since we're using Jodit editor directly\r\n\r\n\t\t// Auto-focus the editor when editing mode is activated\r\n\t\tuseEffect(() => {\r\n\t\t\tif (isEditing && editorRef.current) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t(editorRef.current as any).editor.focus();\r\n\t\t\t\t}, 50);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\t// Handle clicks outside the editor to close editing mode\r\n\t\tuseEffect(() => {\r\n\t\t\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\t\t\tconst isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n\t\t\t\tconst isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n\t\t\t\t// Check if the target is inside the editor or related elements\r\n\t\t\t\tif (\r\n\t\t\t\t\tcontainerRef.current &&\r\n\t\t\t\t\t!containerRef.current.contains(event.target as Node) && // Click outside the editor container\r\n\t\t\t\t\t!isInsidePopup && // Click outside the popup\r\n\t\t\t\t\t!isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n\t\t\t\t\t!isInsideWorkplacePopup && // Click outside the workplace popup\r\n\t\t\t\t\t!isSelectionMarker && // Click outside selection markers\r\n\t\t\t\t\t!isLinkPopup && // Click outside link input popup\r\n\t\t\t\t\t!isInsideToolbarButton &&// Click outside the toolbar button\r\n\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t!isInsideJoditPopupContent &&\r\n\t\t\t\t\t!isInsideAltTextPopup\r\n\t\t\t\t) {\r\n\t\t\t\t\tsetIsEditing(false); // Close the editor if clicked outside\r\n\t\t\t\t\tsetToolbarVisible(false); // Also hide toolbar when clicking outside\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tif (isEditing) {\r\n\t\t\t\tdocument.addEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t\treturn () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t//padding: 0,\r\n\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\ttransition: \"border 0.2s ease-in-out\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor || \"defaultColor\",\r\n\t\t\t\t\t\t//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\r\n\t\t\t\t\t\t// padding: `${bpadding}px !important` || \"0\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-rte\"\r\n\t\t\t\t\tid=\"rte-box\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* RTE Container with fixed positioned hover icons */}\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tcolor: \"#000000\",\r\n\t\t\t\t\t\t\tbackgroundColor: \"#ffffff\"\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tclassName=\"rte-container\"\r\n\t\t\t\t\t\tonMouseEnter={() => {\r\n\t\t\t\t\t\t\t// Show action icons on hover - wait for workplace to be available\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tconst workplace = document.querySelector(`[data-rte-id=\"${id}\"] .jodit-workplace`);\r\n\t\t\t\t\t\t\t\tif (workplace) {\r\n\t\t\t\t\t\t\t\t\tconst actionIcons = workplace.querySelectorAll('.rte-action-icon');\r\n\t\t\t\t\t\t\t\t\tactionIcons.forEach(icon => {\r\n\t\t\t\t\t\t\t\t\t\t(icon as HTMLElement).style.opacity = '1';\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonMouseLeave={() => {\r\n\t\t\t\t\t\t\t// Hide action icons when not hovering\r\n\t\t\t\t\t\t\tconst workplace = document.querySelector(`[data-rte-id=\"${id}\"] .jodit-workplace`);\r\n\t\t\t\t\t\t\tif (workplace) {\r\n\t\t\t\t\t\t\t\tconst actionIcons = workplace.querySelectorAll('.rte-action-icon');\r\n\t\t\t\t\t\t\t\tactionIcons.forEach(icon => {\r\n\t\t\t\t\t\t\t\t\t(icon as HTMLElement).style.opacity = '0';\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tdata-rte-id={id}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Clone Icon - Fixed at top-right */}\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={() => handleRTECloneSection(id)}\r\n\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3}\r\n\t\t\t\t\t\t\ttitle={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n\t\t\t\t\t\t\tclassName=\"rte-action-icon\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"8px\",\r\n\t\t\t\t\t\t\t\tright: \"15px\",\r\n\t\t\t\t\t\t\t\tzIndex: 1002,\r\n\t\t\t\t\t\t\t\topacity: 0,\r\n\t\t\t\t\t\t\t\ttransition: \"opacity 0.2s ease-in-out\",\r\n\t\t\t\t\t\t\t\twidth: \"28px\",\r\n\t\t\t\t\t\t\t\theight: \"28px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\theight: \"16px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"16px\",\r\n\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\theight: '16px',\r\n\t\t\t\t\t\t\t\t\twidth: '16px',\r\n\t\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\t\tjustifyContent: 'center'\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t{/* Delete Icon - Fixed at top-right, next to clone */}\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={() => handleRTEDeleteSection(id)}\r\n\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}\r\n\t\t\t\t\t\t\ttitle={translate(\"Delete Section\")}\r\n\t\t\t\t\t\t\tclassName=\"rte-action-icon\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"8px\",\r\n\t\t\t\t\t\t\t\tright: \"47px\", // 15px + 28px (clone width) + 4px spacing\r\n\t\t\t\t\t\t\t\tzIndex: 1002,\r\n\t\t\t\t\t\t\t\topacity: 0,\r\n\t\t\t\t\t\t\t\ttransition: \"opacity 0.2s ease-in-out\",\r\n\t\t\t\t\t\t\t\twidth: \"28px\",\r\n\t\t\t\t\t\t\t\theight: \"28px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 0, 0, 0.1) !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\theight: \"16px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"16px\",\r\n\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\theight: '16px',\r\n\t\t\t\t\t\t\t\t\twidth: '16px',\r\n\t\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\t\tjustifyContent: 'center'\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\r\n\r\n\t\t\t\t\t\t\t<style>\r\n\t\t\t\t\t\t\t\t{dynamicCSS}\r\n\t\t\t\t\t\t\t</style>\r\n\t\t\t\t\t\t\t<JoditEditor\r\n\t\t\t\t\t\t\t\tref={editorRef}\r\n\t\t\t\t\t\t\t\tvalue={rteBoxValue || \"\"}\r\n\t\t\t\t\t\t\t\tconfig={joditConfig}\r\n\t\t\t\t\t\t\t\tonChange={handleContentChange}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t{/* Edit Icon - Always visible with dynamic positioning */}\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\ttoggleToolbar();\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t// Dynamic positioning: when toolbar is visible, always bottom-right\r\n\t\t\t\t\t\t\t\t\tbottom: toolbarVisible ? \"8px\" : (contentState.isEmpty ? \"50%\" : (contentState.isScrollable ? \"8px\" : \"2px\")),\r\n\t\t\t\t\t\t\t\t\tright: toolbarVisible ? \"2px\" : (contentState.isEmpty ? \"auto\" : \"2px\"),\r\n\t\t\t\t\t\t\t\t\tleft: toolbarVisible ? \"auto\" : (contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\"),\r\n\t\t\t\t\t\t\t\t\ttransform: toolbarVisible ? \"none\" : (contentState.isEmpty ? \"translateY(50%)\" : \"none\"),\r\n\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n\t\t\t\t\t\t\t\t\tzIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 1)\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\twidth: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"16px\",\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Toggle Toolbar\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: editicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: '16px', width: '16px' }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</Box>\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n);\r\n\r\nexport default memo(RTEsection);\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAaC,IAAI,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7G,SAASC,GAAG,EAAuBC,UAAU,QAAQ,eAAe;AACpE,OAAOC,WAAW,MAAM,aAAa;AAGrC,OAAOC,cAAc,MAAuC,+BAA+B;AAI3F,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,gCAAgC;AAC/E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAa/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGlB,UAAU,CAAAmB,EAAA,GAAAD,EAAA,CACvD,CACC;EACCE,KAAK,EAAE;IAAEC,EAAE;IAAEC;EAAY,CAAC;EAC1BC,MAAM;EACNC,WAAW;EACXC,WAAW;EAEXC,aAAa;EACbC,gBAAgB;EAChBC;AACD,CAAC,EACDC,GAAG,KACC;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EAAArB,EAAA;EACJ,MAAM;IAAEsB,CAAC,EAAEC;EAAU,CAAC,GAAG7B,cAAc,CAAC,CAAC;EACzC,MAAM;IACL8B,mBAAmB;IACnBC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,QAAQ;IACRC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,qBAAqB;IACrBC,sBAAsB;IACtBC,qBAAqB;IACrBC,OAAO;IACPC,WAAW;IACXC;EACD,CAAC,GAAGnD,cAAc,CAAEoD,KAAK,IAAKA,KAAK,CAAC;EACpC;;EAEA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMmE,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiE,YAAY,GAAGjE,MAAM,CAAwB,IAAI,CAAC;;EAExD;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAA8C;IAAEuE,OAAO,EAAE,IAAI;IAAEC,YAAY,EAAE;EAAM,CAAC,CAAC;;EAErI;EACA,MAAMC,WAAW,GAAGpE,OAAO,CAAC,OAAY;IACvCqE,QAAQ,EAAE,KAAK;IACf;IACAC,OAAO,EAAEV,cAAc;IACvB;IACA;IACA;IACA;IACA;IACAW,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtB;IACAC,iBAAiB,EAAE,OAAO;IAC1BC,kBAAkB,EAAE,GAAG;IACvBC,qBAAqB,EAAE,GAAG;IAC1BC,qBAAqB,EAAE,GAAG;IAC1B;IACAC,KAAK,EAAE;MACNC,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;MAC5GR,OAAO,EAAE;QACRS,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE;MACX;IACD,CAAC;IACDC,gBAAgB,EAAE,KAAK;IACvBC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC7BC,OAAO,EAAE,CACR,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACCC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACL,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAClD,SAAS,EAAE,QAAQ,EAAE,WAAW;IAElC,CAAC,CACD;IACDC,SAAS,EAAE,KAAK;IAChB;IACAC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAEnC,cAAc,GAAG,GAAG,GAAG,EAAE;IAAE;IACtCoC,SAAS,EAAE,GAAG;IACd;IACAC,SAAS,EAAEC,QAAQ,CAACC,IAAI;IACxB;IACAC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACAC,KAAK,EAAE;MACNC,KAAK,EAAE,oBAAoB;MAC3BC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE;IACb,CAAC;IACD;IACAC,cAAc,EAAE,sBAAsB;IACtC;IACAC,KAAK,EAAE,GAAY;IACnB;IACAC,IAAI,EAAE;MACLC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IAChB,CAAC;IACD;IACAC,MAAM,EAAE;MACPf,MAAM,EAAE;IACT,CAAC;IACDgB,QAAQ,EAAE;MACTC,IAAI,EAAE;QACLzB,IAAI,EAAE;UACL,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAC/B;MACD;IACD;EACD,CAAC,CAAC,EAAE,CAAC1E,EAAE,EAAE0C,cAAc,CAAC,CAAC;;EAEzB;EACA,MAAM0D,cAAc,GAAIC,OAAe,IAAc;IACpD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,OAAOF,WAAW,CAACG,MAAM,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAe;IAC1C,IAAI7D,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE8D,OAAO,EAAE;MAC1B,MAAMC,SAAS,GAAG/D,YAAY,CAAC8D,OAAO,CAACE,aAAa,CAAC,kBAAkB,CAAC;MACxE,IAAID,SAAS,EAAE;QACd,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;MACvD;IACD;IACA,OAAO,KAAK;EACb,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIX,OAAe,IAAK;IAC/C,MAAMrD,OAAO,GAAGoD,cAAc,CAACC,OAAO,CAAC;IACvC,MAAMpD,YAAY,GAAGyD,mBAAmB,CAAC,CAAC;IAE1C3D,eAAe,CAAC;MAAEC,OAAO;MAAEC;IAAa,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMgE,aAAa,GAAGA,CAAA,KAAM;IAC3BtE,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACnC,CAAC;;EAED;EACA,MAAMwE,UAAU,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBxE,cAAc,GAAG,OAAO,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,MAAMyE,mBAAmB,GAAGpI,WAAW,CAAEqI,UAAkB,IAAK;IAC/DnF,qBAAqB,CAACjC,EAAE,EAAEoH,UAAU,CAAC;IACrC;IACAJ,kBAAkB,CAACI,UAAU,CAAC;EAC/B,CAAC,EAAE,CAACpH,EAAE,EAAEiC,qBAAqB,EAAE+E,kBAAkB,CAAC,CAAC;;EAEnD;EACAtI,SAAS,CAAC,MAAM;IACfsI,kBAAkB,CAAC/G,WAAW,IAAI,EAAE,CAAC;EACtC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACAvB,SAAS,CAAC,MAAM;IACf,IAAI8D,SAAS,IAAII,SAAS,CAAC+D,OAAO,EAAE;MACnCU,UAAU,CAAC,MAAM;QACfzE,SAAS,CAAC+D,OAAO,CAASW,MAAM,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC,EAAE,CAAC/E,SAAS,CAAC,CAAC;;EAEf;EACA9D,SAAS,CAAC,MAAM;IACf,MAAM8I,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjD,MAAMC,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAG1C,QAAQ,CAAC6B,aAAa,CAAC,cAAc,CAAC,cAAAa,qBAAA,uBAAtCA,qBAAA,CAAwCS,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMK,kBAAkB,IAAAT,sBAAA,GAAG3C,QAAQ,CAAC6B,aAAa,CAAC,gBAAgB,CAAC,cAAAc,sBAAA,uBAAxCA,sBAAA,CAA0CQ,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMM,sBAAsB,GAAGD,kBAAkB,MAAAR,sBAAA,GAAI5C,QAAQ,CAAC6B,aAAa,CAAC,sBAAsB,CAAC,cAAAe,sBAAA,uBAA9CA,sBAAA,CAAgDO,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMO,iBAAiB,GAAIb,KAAK,CAACM,MAAM,CAAiB/H,EAAE,CAACuI,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAX,sBAAA,GAAG7C,QAAQ,CAAC6B,aAAa,CAAC,wBAAwB,CAAC,cAAAgB,sBAAA,uBAAhDA,sBAAA,CAAkDM,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMU,qBAAqB,GAAIhB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMU,cAAc,GAAIjB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,IACCnF,YAAY,CAAC8D,OAAO,IACpB,CAAC9D,YAAY,CAAC8D,OAAO,CAACwB,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MAAI;MACxD,CAACG,aAAa;MAAI;MAClB,CAACE,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACE,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAG;MACzB,CAACC,cAAc,IACf,CAACZ,yBAAyB,IAC1B,CAACG,oBAAoB,EACpB;QACDxF,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACrBE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC3B;IACD,CAAC;IAED,IAAIH,SAAS,EAAE;MACdwC,QAAQ,CAAC2D,gBAAgB,CAAC,WAAW,EAAEnB,kBAAkB,CAAC;MAC1D,OAAO,MAAMxC,QAAQ,CAAC4D,mBAAmB,CAAC,WAAW,EAAEpB,kBAAkB,CAAC;IAC3E;EACD,CAAC,EAAE,CAAChF,SAAS,CAAC,CAAC;EAEf,oBACC/C,OAAA,CAAAE,SAAA;IAAAkJ,QAAA,eACCpJ,OAAA,CAACT,GAAG;MACH8J,EAAE,EAAE;QACHC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,UAAU;QACpB;QACAC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,UAAU,EAAE,yBAAyB;QACrC9D,eAAe,EAAExD,YAAY,IAAI;QACjC;QACA;MACD,CAAE;MACFuH,SAAS,EAAC,WAAW;MACrBrJ,EAAE,EAAC,SAAS;MAAA6I,QAAA,eAGZpJ,OAAA;QACC2F,KAAK,EAAE;UACNvB,KAAK,EAAE,MAAM;UACboF,QAAQ,EAAE,UAAU;UACpB5D,KAAK,EAAE,SAAS;UAChBC,eAAe,EAAE;QAClB,CAAE;QACF+D,SAAS,EAAC,eAAe;QACzBC,YAAY,EAAEA,CAAA,KAAM;UACnB;UACAjC,UAAU,CAAC,MAAM;YAChB,MAAMT,SAAS,GAAG5B,QAAQ,CAAC6B,aAAa,CAAC,iBAAiB7G,EAAE,qBAAqB,CAAC;YAClF,IAAI4G,SAAS,EAAE;cACd,MAAM2C,WAAW,GAAG3C,SAAS,CAAC4C,gBAAgB,CAAC,kBAAkB,CAAC;cAClED,WAAW,CAACE,OAAO,CAACC,IAAI,IAAI;gBAC1BA,IAAI,CAAiBtE,KAAK,CAACuE,OAAO,GAAG,GAAG;cAC1C,CAAC,CAAC;YACH;UACD,CAAC,EAAE,GAAG,CAAC;QACR,CAAE;QACFC,YAAY,EAAEA,CAAA,KAAM;UACnB;UACA,MAAMhD,SAAS,GAAG5B,QAAQ,CAAC6B,aAAa,CAAC,iBAAiB7G,EAAE,qBAAqB,CAAC;UAClF,IAAI4G,SAAS,EAAE;YACd,MAAM2C,WAAW,GAAG3C,SAAS,CAAC4C,gBAAgB,CAAC,kBAAkB,CAAC;YAClED,WAAW,CAACE,OAAO,CAACC,IAAI,IAAI;cAC1BA,IAAI,CAAiBtE,KAAK,CAACuE,OAAO,GAAG,GAAG;YAC1C,CAAC,CAAC;UACH;QACD,CAAE;QACF,eAAa3J,EAAG;QAAA6I,QAAA,gBAGhBpJ,OAAA,CAACR,UAAU;UACV4K,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAM3H,qBAAqB,CAACnC,EAAE,CAAE;UACzC+J,QAAQ,EAAE,EAAAtJ,qBAAA,GAAA6B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA5B,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCuJ,UAAU,cAAAtJ,sBAAA,uBAAjDA,sBAAA,CAAmD+F,MAAM,KAAI,CAAE;UACzEwD,KAAK,EAAE,EAAAtJ,sBAAA,GAAA2B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA1B,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCqJ,UAAU,cAAApJ,sBAAA,uBAAjDA,sBAAA,CAAmD6F,MAAM,KAAI,CAAC,GAAGrF,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAChKiI,SAAS,EAAC,iBAAiB;UAC3BP,EAAE,EAAE;YACHG,QAAQ,EAAE,UAAU;YACpBiB,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE,MAAM;YACbjF,MAAM,EAAE,IAAI;YACZyE,OAAO,EAAE,CAAC;YACVP,UAAU,EAAE,0BAA0B;YACtCvF,KAAK,EAAE,MAAM;YACbe,MAAM,EAAE,MAAM;YACdU,eAAe,EAAE,0BAA0B;YAC3C,SAAS,EAAE;cACVA,eAAe,EAAE;YAClB,CAAC;YACD8E,GAAG,EAAE;cACJxF,MAAM,EAAE,MAAM;cACdf,KAAK,EAAE,MAAM;cACbwG,IAAI,EAAE;gBACLC,IAAI,EAAC;cACN;YACD;UACD,CAAE;UAAAzB,QAAA,eAEFpJ,OAAA;YACC8K,uBAAuB,EAAE;cAAEC,MAAM,EAAEpL;YAAS,CAAE;YAC9CgG,KAAK,EAAE;cACNuE,OAAO,EAAE,EAAA9I,sBAAA,GAAAyB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCmJ,UAAU,cAAAlJ,sBAAA,uBAAjDA,sBAAA,CAAmD2F,MAAM,KAAI,CAAC,GAAG,GAAG,GAAG,CAAC;cACjF7B,MAAM,EAAE,MAAM;cACdf,KAAK,EAAE,MAAM;cACbkF,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByB,cAAc,EAAE;YACjB;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAGbpL,OAAA,CAACR,UAAU;UACV4K,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAM5H,sBAAsB,CAAClC,EAAE,CAAE;UAC1C+J,QAAQ,EAAE,EAAAhJ,sBAAA,GAAAuB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCiJ,UAAU,cAAAhJ,sBAAA,uBAAjDA,sBAAA,CAAmDyF,MAAM,MAAK,CAAE;UAC1EwD,KAAK,EAAE7I,SAAS,CAAC,gBAAgB,CAAE;UACnCiI,SAAS,EAAC,iBAAiB;UAC3BP,EAAE,EAAE;YACHG,QAAQ,EAAE,UAAU;YACpBiB,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE,MAAM;YAAE;YACfjF,MAAM,EAAE,IAAI;YACZyE,OAAO,EAAE,CAAC;YACVP,UAAU,EAAE,0BAA0B;YACtCvF,KAAK,EAAE,MAAM;YACbe,MAAM,EAAE,MAAM;YACdU,eAAe,EAAE,0BAA0B;YAC3C,SAAS,EAAE;cACVA,eAAe,EAAE;YAClB,CAAC;YACD8E,GAAG,EAAE;cACJxF,MAAM,EAAE,MAAM;cACdf,KAAK,EAAE,MAAM;cACbwG,IAAI,EAAE;gBACLC,IAAI,EAAC;cACN;YACD;UACD,CAAE;UAAAzB,QAAA,eAEFpJ,OAAA;YACC8K,uBAAuB,EAAE;cAAEC,MAAM,EAAEnL;YAAW,CAAE;YAChD+F,KAAK,EAAE;cACNuE,OAAO,EAAE,EAAA1I,sBAAA,GAAAqB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAApB,sBAAA,wBAAAC,uBAAA,GAArCD,sBAAA,CAAuC+I,UAAU,cAAA9I,uBAAA,uBAAjDA,uBAAA,CAAmDuF,MAAM,MAAK,CAAC,GAAG,GAAG,GAAG,CAAC;cAClF7B,MAAM,EAAE,MAAM;cACdf,KAAK,EAAE,MAAM;cACbkF,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByB,cAAc,EAAE;YACjB;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAGZpL,OAAA;UAAAoJ,QAAA,EACE3B;QAAU;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACRpL,OAAA,CAACP,WAAW;UACXsB,GAAG,EAAEoC,SAAU;UACfkI,KAAK,EAAE7K,WAAW,IAAI,EAAG;UACzB8K,MAAM,EAAE7H,WAAY;UACpB8H,QAAQ,EAAE7D;QAAoB;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAGFpL,OAAA,CAACR,UAAU;UACV4K,IAAI,EAAC,OAAO;UACZC,OAAO,EAAGmB,CAAC,IAAK;YACfA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBjE,aAAa,CAAC,CAAC;UAChB,CAAE;UACF6B,EAAE,EAAE;YACHG,QAAQ,EAAE,UAAU;YACpB;YACAkC,MAAM,EAAEzI,cAAc,GAAG,KAAK,GAAII,YAAY,CAACE,OAAO,GAAG,KAAK,GAAIF,YAAY,CAACG,YAAY,GAAG,KAAK,GAAG,KAAO;YAC7GkH,KAAK,EAAEzH,cAAc,GAAG,KAAK,GAAII,YAAY,CAACE,OAAO,GAAG,MAAM,GAAG,KAAM;YACvEoI,IAAI,EAAE1I,cAAc,GAAG,MAAM,GAAII,YAAY,CAACE,OAAO,GAAG,mBAAmB,GAAG,MAAO;YACrFqI,SAAS,EAAE3I,cAAc,GAAG,MAAM,GAAII,YAAY,CAACE,OAAO,GAAG,iBAAiB,GAAG,MAAO;YACxFa,KAAK,EAAE,MAAM;YACbe,MAAM,EAAE,MAAM;YACdU,eAAe,EAAE,0BAA0B;YAC3CJ,MAAM,EAAEpC,YAAY,CAACG,YAAY,GAAG,IAAI,GAAG,IAAI;YAAE;YACjD,SAAS,EAAE;cACVqC,eAAe,EAAE;YAClB,CAAC;YACD,OAAO,EAAE;cACRzB,KAAK,EAAE,MAAM;cACbe,MAAM,EAAE;YACT;UACD,CAAE;UACFqF,KAAK,EAAE7I,SAAS,CAAC,gBAAgB,CAAE;UAAAyH,QAAA,eAEnCpJ,OAAA;YACC8K,uBAAuB,EAAE;cAAEC,MAAM,EAAElL;YAAS,CAAE;YAC9C8F,KAAK,EAAE;cAAER,MAAM,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAO;UAAE;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACL,CAAC;AAEL,CAAC;EAAA,QAveyBtL,cAAc,EAoBnCJ,cAAc;AAAA,EAodpB,CAAC;EAAA,QAxe0BI,cAAc,EAoBnCJ,cAAc;AAAA,EAodnB;AAACmM,GAAA,GAtfI1L,UAAqC;AAwf3C,eAAA2L,GAAA,gBAAe1M,IAAI,CAACe,UAAU,CAAC;AAAC,IAAAE,EAAA,EAAAwL,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA1L,EAAA;AAAA0L,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}