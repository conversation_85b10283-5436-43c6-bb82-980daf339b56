{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\RTE\\\\RTESection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useRef, memo, useMemo, useCallback } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  items: {\n    id,\n    rteBoxValue\n  },\n  boxRef,\n  handleFocus,\n  handleeBlur,\n  isPopoverOpen,\n  setIsPopoverOpen,\n  currentRTEFocusedId\n}, ref) => {\n  var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6, _toolTipGuideMetaData7, _toolTipGuideMetaData8, _toolTipGuideMetaData9, _toolTipGuideMetaData10;\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setIsUnSavedChanges,\n    setHtmlContent,\n    textvaluess,\n    setTextvaluess,\n    backgroundC,\n    setBackgroundC,\n    Bbordercolor,\n    BborderSize,\n    bpadding,\n    sectionColor,\n    setSectionColor,\n    handleTooltipRTEBlur,\n    handleTooltipRTEValue,\n    handleRTEDeleteSection,\n    handleRTECloneSection,\n    tooltip,\n    currentStep,\n    toolTipGuideMetaData\n  } = useDrawerStore(state => state);\n  // Removed unused state variables since we're using Jodit editor directly\n\n  // Memoize Jodit config to prevent re-renders and focus loss\n  const joditConfig = useMemo(() => ({\n    readonly: false,\n    // Hide main toolbar by default\n    toolbar: false,\n    // Enable inline toolbar for text selection\n    toolbarInline: true,\n    toolbarInlineForSelection: true,\n    toolbarInlineDisabledButtons: ['source', 'fullsize'],\n    toolbarInlineDisableFor: [],\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    // Inline toolbar width configuration\n    toolbarButtonSize: 'small',\n    toolbarInlineWidth: 500,\n    toolbarInlineMaxWidth: 600,\n    toolbarInlineMinWidth: 450,\n    // Additional popup configuration for inline toolbar\n    popup: {\n      selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\n      toolbar: {\n        width: 500,\n        maxWidth: 600,\n        minWidth: 450\n      }\n    },\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    statusbar: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: false,\n    // Enable auto-resize behavior\n    height: 'auto',\n    minHeight: 28,\n    maxHeight: 112,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Ensure dialogs appear in correct position\n    zIndex: 100000,\n    globalFullSize: false,\n    // Add custom CSS to ensure text is visible\n    style: {\n      color: '#000000 !important',\n      backgroundColor: '#ffffff',\n      fontFamily: 'Poppins, sans-serif'\n    },\n    // Override editor styles to ensure text visibility\n    editorCssClass: 'jodit-tooltip-editor',\n    // Set default content styling\n    enter: 'p',\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [id]);\n  const [isEditing, setIsEditing] = useState(false);\n  const editorRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // State to track content for dynamic icon positioning\n  const [contentState, setContentState] = useState({\n    isEmpty: true,\n    isScrollable: false\n  });\n\n  // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\n  const isContentEmpty = content => {\n    if (!content) return true;\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    return textContent.length === 0;\n  };\n\n  // Helper function to check if content is scrollable\n  const isContentScrollable = () => {\n    if (containerRef !== null && containerRef !== void 0 && containerRef.current) {\n      const workplace = containerRef.current.querySelector('.jodit-workplace');\n      if (workplace) {\n        return workplace.scrollHeight > workplace.clientHeight;\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = content => {\n    const isEmpty = isContentEmpty(content);\n    const isScrollable = isContentScrollable();\n    setContentState({\n      isEmpty,\n      isScrollable\n    });\n  };\n\n  // Memoize onChange handler to prevent re-renders\n  const handleContentChange = useCallback(newContent => {\n    handleTooltipRTEValue(id, newContent);\n    // Update content state for dynamic icon positioning\n    updateContentState(newContent);\n  }, [id, handleTooltipRTEValue, updateContentState]);\n\n  // Initialize content state on mount\n  useEffect(() => {\n    updateContentState(rteBoxValue || \"\");\n  }, [rteBoxValue]);\n\n  // const handleInput = () => {\n  // \t// Update the content state when user types\n  // \tif (boxRef.current) {\n  // \t\tconst updatedContent = boxRef.current.innerHTML;\n  // \t\tsetContent(updatedContent); // Store the content in state\n  // \t\tsetHtmlContent(updatedContent); // Update the HTML content\n  // \t\tsetIsUnSavedChanges(true);\n  // \t\tpreserveCaretPosition();\n  // \t}\n  // };\n  // Removed caret position functions since we're using Jodit editor\n\n  // useEffect(() => {\n  // \t// After content update, restore the cursor position\n  // \trestoreCaretPosition();\n  // }, [boxRef.current?.innerHTML]); // Run when content changes\n\n  // Remove section\n\n  // useEffect(() => {\n  // \tif (boxRef.current?.innerHTML?.trim()) {\n  // \t\tsetIsUnSavedChanges(true);\n  // \t}\n  // }, [boxRef.current?.innerHTML?.trim()]);\n\n  // Removed useEffect since we're using Jodit editor directly\n\n  // Auto-focus the editor when editing mode is activated\n  useEffect(() => {\n    if (isEditing && editorRef.current) {\n      setTimeout(() => {\n        editorRef.current.editor.focus();\n      }, 50);\n    }\n  }, [isEditing]);\n\n  // Handle clicks outside the editor to close editing mode\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Check if the target is inside the editor or related elements\n      if (containerRef.current && !containerRef.current.contains(event.target) &&\n      // Click outside the editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setIsEditing(false); // Close the editor if clicked outside\n      }\n    };\n    if (isEditing) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        position: \"relative\",\n        //padding: 0,\n        margin: 0,\n        boxSizing: \"border-box\",\n        transition: \"border 0.2s ease-in-out\",\n        backgroundColor: sectionColor || \"defaultColor\"\n        //border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\n        // padding: `${bpadding}px !important` || \"0\",\n      },\n      className: \"qadpt-rte\",\n      id: \"rte-box\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: \"100%\",\n          position: \"relative\",\n          color: \"#000000\",\n          backgroundColor: \"#ffffff\"\n        },\n        className: \"rte-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rte-action-icons\",\n          style: {\n            position: \"absolute\",\n            top: \"8px\",\n            right: \"15px\",\n            zIndex: 1000,\n            display: \"flex\",\n            opacity: 0,\n            transition: \"opacity 0.2s ease-in-out\",\n            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n            borderRadius: \"4px\",\n            padding: \"2px\",\n            boxShadow: \"rgba(0, 0, 0, 0.1) 1px 1px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTECloneSection(id),\n            disabled: ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.length) >= 3,\n            title: ((_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.length) >= 3 ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n            sx: {\n              width: \"24px\",\n              height: \"24px\",\n              padding: \"2px\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\"\n              },\n              svg: {\n                height: \"14px\",\n                width: \"14px\",\n                path: {\n                  fill: \"var(--primarycolor)\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.length) >= 3 ? 0.5 : 1,\n                height: '12px',\n                width: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTEDeleteSection(id),\n            disabled: ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : (_toolTipGuideMetaData8 = _toolTipGuideMetaData7.containers) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.length) === 1,\n            title: translate(\"Delete Section\"),\n            sx: {\n              width: \"24px\",\n              height: \"24px\",\n              padding: \"2px\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 0, 0, 0.1) !important\"\n              },\n              svg: {\n                height: \"14px\",\n                width: \"14px\",\n                path: {\n                  fill: \"#ff4444\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData9 === void 0 ? void 0 : (_toolTipGuideMetaData10 = _toolTipGuideMetaData9.containers) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.length) === 1 ? 0.5 : 1,\n                height: '14px',\n                width: '14px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 8\n          }, this), contentState.isEmpty && /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: e => {\n              e.stopPropagation();\n              setIsEditing(!isEditing);\n            },\n            sx: {\n              width: \"24px\",\n              height: \"24px\",\n              padding: \"2px\",\n              \"&:hover\": {\n                backgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\"\n              },\n              \"& svg\": {\n                width: \"14px\",\n                height: \"14px\"\n              }\n            },\n            title: translate(\"Toggle Toolbar\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: editicon\n              },\n              style: {\n                height: '12px',\n                width: '12px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n\t\t\t\t\t\t\t\t\t\t/* Hide the add new line button */\n\t\t\t\t\t\t\t\t\t\t.jodit-add-new-line {\n\t\t\t\t\t\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\n\t\t\t\t\t\t\t\t\t\tpadding: 8px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Height and scrolling behavior for tooltip RTE */\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace {\n\t\t\t\t\t\t\t\t\t\tmin-height: 28px !important;\n\t\t\t\t\t\t\t\t\t\tmax-height: 112px !important;\n\t\t\t\t\t\t\t\t\t\toverflow-y: auto !important;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-container {\n\t\t\t\t\t\t\t\t\t\tminHeight: \"28px !important\",\n\t\t\t\t\t\t\t\t\t\tborder: \"none !important\"\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Target the specific jodit container class combination */\n\t\t\t\t\t\t\t\t\t.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {\n\t\t\t\t\t\t\t\t\t\tborder: 0 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg p {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tmargin: 0 0 4px 0 !important;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Enhanced scrollbar styling for tooltip RTE */\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar {\n\t\t\t\t\t\t\t\t\t\twidth: 6px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-track {\n\t\t\t\t\t\t\t\t\t\tbackground: #f1f1f1 !important;\n\t\t\t\t\t\t\t\t\t\tborder-radius: 3px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb {\n\t\t\t\t\t\t\t\t\t\tbackground: #c1c1c1 !important;\n\t\t\t\t\t\t\t\t\t\tborder-radius: 3px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb:hover {\n\t\t\t\t\t\t\t\t\t\tbackground: #a8a8a8 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg div {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg span {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg br {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Override any inherited styles from tooltip/modal */\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Ensure text is visible in all states */\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\n\t\t\t\t\t\t\t\t\t\tpadding: 8px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] p {\n\t\t\t\t\t\t\t\t\t\tmargin: 0 0 4px 0 !important;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Override any modal or tooltip text color inheritance */\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg,\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Fix Jodit dialog positioning - target correct classes */\n\t\t\t\t\t\t\t\t\t.jodit.jodit-dialog {\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-dialog .jodit-dialog__panel {\n\t\t\t\t\t\t\t\t\t\tposition: relative !important;\n\t\t\t\t\t\t\t\t\t\ttop: auto !important;\n\t\t\t\t\t\t\t\t\t\tleft: auto !important;\n\t\t\t\t\t\t\t\t\t\ttransform: none !important;\n\t\t\t\t\t\t\t\t\t\tmax-width: 400px !important;\n\t\t\t\t\t\t\t\t\t\tbackground: white !important;\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ccc !important;\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Fix for link dialog specifically */\n\t\t\t\t\t\t\t\t\t.jodit-dialog_alert {\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Style the inline toolbar */\n\t\t\t\t\t\t\t\t\t.jodit-toolbar-popup {\n\t\t\t\t\t\t\t\t\t\tbackground-color: white !important;\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ddd !important;\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;\n\t\t\t\t\t\t\t\t\t\tz-index: 100002 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-toolbar-popup .jodit-toolbar__box {\n\t\t\t\t\t\t\t\t\t\tbackground-color: white !important;\n\t\t\t\t\t\t\t\t\t\tpadding: 4px !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* RTE container hover styles */\n\t\t\t\t\t\t\t\t\t.rte-container:hover .rte-action-icons {\n\t\t\t\t\t\t\t\t\t\topacity: 1 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.rte-action-icons {\n\t\t\t\t\t\t\t\t\t\topacity: 0 !important;\n\t\t\t\t\t\t\t\t\t\ttransition: opacity 0.2s ease-in-out !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n          value: rteBoxValue || \"\",\n          config: joditConfig,\n          onChange: handleContentChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}, \"8Vm8sdiNIdJiqpIO4XIlQxwh3B0=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"8Vm8sdiNIdJiqpIO4XIlQxwh3B0=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default _c3 = /*#__PURE__*/memo(RTEsection);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useRef", "memo", "useMemo", "useCallback", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "items", "id", "rteBoxValue", "boxRef", "handleFocus", "handleeBlur", "isPopoverOpen", "setIsPopoverOpen", "currentRTEFocusedId", "ref", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "t", "translate", "setIsUnSavedChanges", "setHtmlContent", "textvaluess", "setTextvaluess", "backgroundC", "setBackgroundC", "Bbordercolor", "BborderSize", "bpadding", "sectionColor", "setSectionColor", "handleTooltipRTEBlur", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "currentStep", "toolTipGuideMetaData", "state", "joditConfig", "readonly", "toolbar", "toolbarInline", "toolbarInlineForSelection", "toolbarInlineDisabledButtons", "toolbarInlineDisableFor", "toolbarSticky", "toolbarAdaptive", "toolbarButtonSize", "toolbarInlineWidth", "toolbarInlineMaxWidth", "toolbarInlineMinWidth", "popup", "selection", "width", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "buttons", "name", "iconURL", "list", "autofocus", "height", "minHeight", "maxHeight", "popupRoot", "document", "body", "zIndex", "globalFullSize", "style", "color", "backgroundColor", "fontFamily", "editor<PERSON>s<PERSON><PERSON>", "enter", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "controls", "font", "isEditing", "setIsEditing", "editor<PERSON><PERSON>", "containerRef", "contentState", "setContentState", "isEmpty", "isScrollable", "isContentEmpty", "content", "textContent", "replace", "trim", "length", "isContentScrollable", "current", "workplace", "querySelector", "scrollHeight", "clientHeight", "updateContentState", "handleContentChange", "newContent", "setTimeout", "editor", "focus", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "addEventListener", "removeEventListener", "children", "sx", "display", "alignItems", "position", "margin", "boxSizing", "transition", "className", "top", "right", "opacity", "borderRadius", "padding", "boxShadow", "size", "onClick", "disabled", "containers", "title", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "justifyContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "stopPropagation", "value", "config", "onChange", "_c2", "_c3", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/RTE/RTESection.tsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from \"react\";\r\nimport { Box, Popover, Typography, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\n\r\nimport RTE from \"./RTE\";\r\nimport useDrawerStore, { IRTEContainer, TSectionType } from \"../../../../store/drawerStore\";\r\nimport { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from \"@mui/icons-material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n\titems: IRTEContainer;\r\n\tboxRef: React.RefObject<HTMLDivElement>;\r\n\thandleFocus: (id: string) => void;\r\n\thandleeBlur: (id: string) => void;\r\n\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tcurrentRTEFocusedId: string;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n\t(\r\n\t\t{\r\n\t\t\titems: { id, rteBoxValue },\r\n\t\t\tboxRef,\r\n\t\t\thandleFocus,\r\n\t\t\thandleeBlur,\r\n\r\n\t\t\tisPopoverOpen,\r\n\t\t\tsetIsPopoverOpen,\r\n\t\t\tcurrentRTEFocusedId,\r\n\t\t},\r\n\t\tref\r\n\t) => {\r\n\t\tconst { t: translate } = useTranslation();\r\n\t\tconst {\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tsetHtmlContent,\r\n\t\t\ttextvaluess,\r\n\t\t\tsetTextvaluess,\r\n\t\t\tbackgroundC,\r\n\t\t\tsetBackgroundC,\r\n\t\t\tBbordercolor,\r\n\t\t\tBborderSize,\r\n\t\t\tbpadding,\r\n\t\t\tsectionColor,\r\n\t\t\tsetSectionColor,\r\n\t\t\thandleTooltipRTEBlur,\r\n\t\t\thandleTooltipRTEValue,\r\n\t\t\thandleRTEDeleteSection,\r\n\t\t\thandleRTECloneSection,\r\n\t\t\ttooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t} = useDrawerStore((state) => state);\r\n\t\t// Removed unused state variables since we're using Jodit editor directly\r\n\r\n\r\n\r\n\t\t// Memoize Jodit config to prevent re-renders and focus loss\r\n\t\tconst joditConfig = useMemo((): any => ({\r\n\t\t\treadonly: false,\r\n\t\t\t// Hide main toolbar by default\r\n\t\t\ttoolbar: false,\r\n\t\t\t// Enable inline toolbar for text selection\r\n\t\t\ttoolbarInline: true,\r\n\t\t\ttoolbarInlineForSelection: true,\r\n\t\t\ttoolbarInlineDisabledButtons: ['source', 'fullsize'],\r\n\t\t\ttoolbarInlineDisableFor: [],\r\n\t\t\ttoolbarSticky: false,\r\n\t\t\ttoolbarAdaptive: false,\r\n\t\t\t// Inline toolbar width configuration\r\n\t\t\ttoolbarButtonSize: 'small',\r\n\t\t\ttoolbarInlineWidth: 500,\r\n\t\t\ttoolbarInlineMaxWidth: 600,\r\n\t\t\ttoolbarInlineMinWidth: 450,\r\n\t\t\t// Additional popup configuration for inline toolbar\r\n\t\t\tpopup: {\r\n\t\t\t\tselection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],\r\n\t\t\t\ttoolbar: {\r\n\t\t\t\t\twidth: 500,\r\n\t\t\t\t\tmaxWidth: 600,\r\n\t\t\t\t\tminWidth: 450\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowCharsCounter: false,\r\n\t\t\tshowWordsCounter: false,\r\n\t\t\tshowXPathInStatusbar: false,\r\n\t\t\tstatusbar: false,\r\n\t\t\tpastePlain: true,\r\n\t\t\taskBeforePasteHTML: false,\r\n\t\t\taskBeforePasteFromWord: false,\r\n\t\t\tbuttons: [\r\n\t\t\t\t'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n\t\t\t\t'font', 'fontsize', 'link',\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'more',\r\n\t\t\t\t\ticonURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n\t\t\t\t\tlist: [\r\n\t\t\t\t\t\t'image', 'video', 'table',\r\n\t\t\t\t\t\t'align', 'undo', 'redo', '|',\r\n\t\t\t\t\t\t'hr', 'eraser', 'copyformat',\r\n\t\t\t\t\t\t'symbol', 'print', 'superscript', 'subscript', '|',\r\n\t\t\t\t\t\t'outdent', 'indent', 'paragraph',\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tautofocus: false,\r\n\t\t\t// Enable auto-resize behavior\r\n\t\t\theight: 'auto',\r\n\t\t\tminHeight: 28,\r\n\t\t\tmaxHeight: 112,\r\n\t\t\t// Fix dialog positioning by setting popup root to document body\r\n\t\t\tpopupRoot: document.body,\r\n\t\t\t// Ensure dialogs appear in correct position\r\n\t\t\tzIndex: 100000,\r\n\t\t\tglobalFullSize: false,\r\n\t\t\t// Add custom CSS to ensure text is visible\r\n\t\t\tstyle: {\r\n\t\t\t\tcolor: '#000000 !important',\r\n\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\tfontFamily: 'Poppins, sans-serif',\r\n\t\t\t},\r\n\t\t\t// Override editor styles to ensure text visibility\r\n\t\t\teditorCssClass: 'jodit-tooltip-editor',\r\n\t\t\t// Set default content styling\r\n\t\t\tenter: 'p' as const,\r\n\t\t\t// Fix link dialog positioning\r\n\t\t\tlink: {\r\n\t\t\t\tfollowOnDblClick: false,\r\n\t\t\t\tprocessVideoLink: true,\r\n\t\t\t\tprocessPastedLink: true,\r\n\t\t\t\topenInNewTabCheckbox: true,\r\n\t\t\t\tnoFollowCheckbox: false,\r\n\t\t\t\tmodeClassName: 'input' as const,\r\n\t\t\t},\r\n\t\t\t// Dialog configuration\r\n\t\t\tdialog: {\r\n\t\t\t\tzIndex: 100001,\r\n\t\t\t},\r\n\t\t\tcontrols: {\r\n\t\t\t\tfont: {\r\n\t\t\t\t\tlist: {\r\n\t\t\t\t\t\t\"Poppins, sans-serif\": \"Poppins\",\r\n\t\t\t\t\t\t\"Roboto, sans-serif\": \"Roboto\",\r\n\t\t\t\t\t\t\"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n\t\t\t\t\t\t\"Open Sans, sans-serif\": \"Open Sans\",\r\n\t\t\t\t\t\t\"Calibri, sans-serif\": \"Calibri\",\r\n\t\t\t\t\t\t\"Century Gothic, sans-serif\": \"Century Gothic\",\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}), [id]);\r\n\r\n\t\tconst [isEditing, setIsEditing] = useState(false);\r\n\t\tconst editorRef = useRef(null);\r\n\t\tconst containerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t\t// State to track content for dynamic icon positioning\r\n\t\tconst [contentState, setContentState] = useState<{ isEmpty: boolean, isScrollable: boolean }>({ isEmpty: true, isScrollable: false });\r\n\r\n\t\t// Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\r\n\t\tconst isContentEmpty = (content: string): boolean => {\r\n\t\t\tif (!content) return true;\r\n\t\t\t// Remove HTML tags and check if there's actual text content\r\n\t\t\tconst textContent = content.replace(/<[^>]*>/g, '').trim();\r\n\t\t\treturn textContent.length === 0;\r\n\t\t};\r\n\r\n\t\t// Helper function to check if content is scrollable\r\n\t\tconst isContentScrollable = (): boolean => {\r\n\t\t\tif (containerRef?.current) {\r\n\t\t\t\tconst workplace = containerRef.current.querySelector('.jodit-workplace');\r\n\t\t\t\tif (workplace) {\r\n\t\t\t\t\treturn workplace.scrollHeight > workplace.clientHeight;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t};\r\n\r\n\t\t// Update content state for dynamic icon positioning\r\n\t\tconst updateContentState = (content: string) => {\r\n\t\t\tconst isEmpty = isContentEmpty(content);\r\n\t\t\tconst isScrollable = isContentScrollable();\r\n\r\n\t\t\tsetContentState({ isEmpty, isScrollable });\r\n\t\t};\r\n\r\n\t\t// Memoize onChange handler to prevent re-renders\r\n\t\tconst handleContentChange = useCallback((newContent: string) => {\r\n\t\t\thandleTooltipRTEValue(id, newContent);\r\n\t\t\t// Update content state for dynamic icon positioning\r\n\t\t\tupdateContentState(newContent);\r\n\t\t}, [id, handleTooltipRTEValue, updateContentState]);\r\n\r\n\t\t// Initialize content state on mount\r\n\t\tuseEffect(() => {\r\n\t\t\tupdateContentState(rteBoxValue || \"\");\r\n\t\t}, [rteBoxValue]);\r\n\r\n\t\t// const handleInput = () => {\r\n\t\t// \t// Update the content state when user types\r\n\t\t// \tif (boxRef.current) {\r\n\t\t// \t\tconst updatedContent = boxRef.current.innerHTML;\r\n\t\t// \t\tsetContent(updatedContent); // Store the content in state\r\n\t\t// \t\tsetHtmlContent(updatedContent); // Update the HTML content\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t\tpreserveCaretPosition();\r\n\t\t// \t}\r\n\t\t// };\r\n\t\t// Removed caret position functions since we're using Jodit editor\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \t// After content update, restore the cursor position\r\n\t\t// \trestoreCaretPosition();\r\n\t\t// }, [boxRef.current?.innerHTML]); // Run when content changes\r\n\r\n\t\t// Remove section\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \tif (boxRef.current?.innerHTML?.trim()) {\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t}\r\n\t\t// }, [boxRef.current?.innerHTML?.trim()]);\r\n\r\n\t\t// Removed useEffect since we're using Jodit editor directly\r\n\r\n\t\t// Auto-focus the editor when editing mode is activated\r\n\t\tuseEffect(() => {\r\n\t\t\tif (isEditing && editorRef.current) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t(editorRef.current as any).editor.focus();\r\n\t\t\t\t}, 50);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\t// Handle clicks outside the editor to close editing mode\r\n\t\tuseEffect(() => {\r\n\t\t\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\t\t\tconst isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n\t\t\t\tconst isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n\t\t\t\t// Check if the target is inside the editor or related elements\r\n\t\t\t\tif (\r\n\t\t\t\t\tcontainerRef.current &&\r\n\t\t\t\t\t!containerRef.current.contains(event.target as Node) && // Click outside the editor container\r\n\t\t\t\t\t!isInsidePopup && // Click outside the popup\r\n\t\t\t\t\t!isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n\t\t\t\t\t!isInsideWorkplacePopup && // Click outside the workplace popup\r\n\t\t\t\t\t!isSelectionMarker && // Click outside selection markers\r\n\t\t\t\t\t!isLinkPopup && // Click outside link input popup\r\n\t\t\t\t\t!isInsideToolbarButton &&// Click outside the toolbar button\r\n\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t!isInsideJoditPopupContent &&\r\n\t\t\t\t\t!isInsideAltTextPopup\r\n\t\t\t\t) {\r\n\t\t\t\t\tsetIsEditing(false); // Close the editor if clicked outside\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tif (isEditing) {\r\n\t\t\t\tdocument.addEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t\treturn () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t//padding: 0,\r\n\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\ttransition: \"border 0.2s ease-in-out\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor || \"defaultColor\",\r\n\t\t\t\t\t\t//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\r\n\t\t\t\t\t\t// padding: `${bpadding}px !important` || \"0\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-rte\"\r\n\t\t\t\t\tid=\"rte-box\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* RTE Container with hover icons */}\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tcolor: \"#000000\",\r\n\t\t\t\t\t\t\tbackgroundColor: \"#ffffff\"\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tclassName=\"rte-container\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Action Icons - positioned at top right, visible only on hover */}\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"rte-action-icons\"\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"8px\",\r\n\t\t\t\t\t\t\t\tright: \"15px\",\r\n\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\topacity: 0,\r\n\t\t\t\t\t\t\t\ttransition: \"opacity 0.2s ease-in-out\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\tpadding: \"2px\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.1) 1px 1px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={() => handleRTECloneSection(id)}\r\n\t\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3}\r\n\t\t\t\t\t\t\t\ttitle={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"2px\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\theight: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\theight: '12px',\r\n\t\t\t\t\t\t\t\t\t\twidth: '12px',\r\n\t\t\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: 'center'\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={() => handleRTEDeleteSection(id)}\r\n\t\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Delete Section\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"2px\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 0, 0, 0.1) !important\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\theight: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff4444\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\theight: '14px',\r\n\t\t\t\t\t\t\t\t\t\twidth: '14px',\r\n\t\t\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: 'center'\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t{/* Edit Icon - Inline with clone/delete when empty */}\r\n\t\t\t\t\t\t\t{contentState.isEmpty && (\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\tsetIsEditing(!isEditing);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"2px\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(var(--primarycolor-rgb), 0.1) !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\ttitle={translate(\"Toggle Toolbar\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: editicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ height: '12px', width: '12px' }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<style>\r\n\t\t\t\t\t\t\t\t{`\r\n\t\t\t\t\t\t\t\t\t\t/* Hide the add new line button */\r\n\t\t\t\t\t\t\t\t\t\t.jodit-add-new-line {\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: none !important;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t\t\t\t\t\t\tpadding: 8px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Height and scrolling behavior for tooltip RTE */\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace {\r\n\t\t\t\t\t\t\t\t\t\tmin-height: 28px !important;\r\n\t\t\t\t\t\t\t\t\t\tmax-height: 112px !important;\r\n\t\t\t\t\t\t\t\t\t\toverflow-y: auto !important;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-container {\r\n\t\t\t\t\t\t\t\t\t\tminHeight: \"28px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborder: \"none !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Target the specific jodit container class combination */\r\n\t\t\t\t\t\t\t\t\t.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {\r\n\t\t\t\t\t\t\t\t\t\tborder: 0 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg p {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tmargin: 0 0 4px 0 !important;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Enhanced scrollbar styling for tooltip RTE */\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar {\r\n\t\t\t\t\t\t\t\t\t\twidth: 6px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-track {\r\n\t\t\t\t\t\t\t\t\t\tbackground: #f1f1f1 !important;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 3px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb {\r\n\t\t\t\t\t\t\t\t\t\tbackground: #c1c1c1 !important;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 3px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb:hover {\r\n\t\t\t\t\t\t\t\t\t\tbackground: #a8a8a8 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg div {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg span {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg br {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Override any inherited styles from tooltip/modal */\r\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Ensure text is visible in all states */\r\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t\t\t\t\t\t\tpadding: 8px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] p {\r\n\t\t\t\t\t\t\t\t\t\tmargin: 0 0 4px 0 !important;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Override any modal or tooltip text color inheritance */\r\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg,\r\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Fix Jodit dialog positioning - target correct classes */\r\n\t\t\t\t\t\t\t\t\t.jodit.jodit-dialog {\r\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-dialog .jodit-dialog__panel {\r\n\t\t\t\t\t\t\t\t\t\tposition: relative !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: auto !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: auto !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: none !important;\r\n\t\t\t\t\t\t\t\t\t\tmax-width: 400px !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground: white !important;\r\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ccc !important;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Fix for link dialog specifically */\r\n\t\t\t\t\t\t\t\t\t.jodit-dialog_alert {\r\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Style the inline toolbar */\r\n\t\t\t\t\t\t\t\t\t.jodit-toolbar-popup {\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: white !important;\r\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ddd !important;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 100002 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-toolbar-popup .jodit-toolbar__box {\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: white !important;\r\n\t\t\t\t\t\t\t\t\t\tpadding: 4px !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* RTE container hover styles */\r\n\t\t\t\t\t\t\t\t\t.rte-container:hover .rte-action-icons {\r\n\t\t\t\t\t\t\t\t\t\topacity: 1 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.rte-action-icons {\r\n\t\t\t\t\t\t\t\t\t\topacity: 0 !important;\r\n\t\t\t\t\t\t\t\t\t\ttransition: opacity 0.2s ease-in-out !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t`}\r\n\t\t\t\t\t\t\t</style>\r\n\t\t\t\t\t\t\t<JoditEditor\r\n\t\t\t\t\t\t\t\tvalue={rteBoxValue || \"\"}\r\n\t\t\t\t\t\t\t\tconfig={joditConfig}\r\n\t\t\t\t\t\t\t\tonChange={handleContentChange}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</Box>\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n);\r\n\r\nexport default memo(RTEsection);\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAaC,IAAI,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7G,SAASC,GAAG,EAAuBC,UAAU,QAAQ,eAAe;AACpE,OAAOC,WAAW,MAAM,aAAa;AAGrC,OAAOC,cAAc,MAAuC,+BAA+B;AAI3F,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,gCAAgC;AAC/E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAa/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGlB,UAAU,CAAAmB,EAAA,GAAAD,EAAA,CACvD,CACC;EACCE,KAAK,EAAE;IAAEC,EAAE;IAAEC;EAAY,CAAC;EAC1BC,MAAM;EACNC,WAAW;EACXC,WAAW;EAEXC,aAAa;EACbC,gBAAgB;EAChBC;AACD,CAAC,EACDC,GAAG,KACC;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EAAArB,EAAA;EACJ,MAAM;IAAEsB,CAAC,EAAEC;EAAU,CAAC,GAAG7B,cAAc,CAAC,CAAC;EACzC,MAAM;IACL8B,mBAAmB;IACnBC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,QAAQ;IACRC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,qBAAqB;IACrBC,sBAAsB;IACtBC,qBAAqB;IACrBC,OAAO;IACPC,WAAW;IACXC;EACD,CAAC,GAAGnD,cAAc,CAAEoD,KAAK,IAAKA,KAAK,CAAC;EACpC;;EAIA;EACA,MAAMC,WAAW,GAAG1D,OAAO,CAAC,OAAY;IACvC2D,QAAQ,EAAE,KAAK;IACf;IACAC,OAAO,EAAE,KAAK;IACd;IACAC,aAAa,EAAE,IAAI;IACnBC,yBAAyB,EAAE,IAAI;IAC/BC,4BAA4B,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;IACpDC,uBAAuB,EAAE,EAAE;IAC3BC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtB;IACAC,iBAAiB,EAAE,OAAO;IAC1BC,kBAAkB,EAAE,GAAG;IACvBC,qBAAqB,EAAE,GAAG;IAC1BC,qBAAqB,EAAE,GAAG;IAC1B;IACAC,KAAK,EAAE;MACNC,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;MAC5GZ,OAAO,EAAE;QACRa,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE;MACX;IACD,CAAC;IACDC,gBAAgB,EAAE,KAAK;IACvBC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC7BC,OAAO,EAAE,CACR,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACCC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACL,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAClD,SAAS,EAAE,QAAQ,EAAE,WAAW;IAElC,CAAC,CACD;IACDC,SAAS,EAAE,KAAK;IAChB;IACAC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,GAAG;IACd;IACAC,SAAS,EAAEC,QAAQ,CAACC,IAAI;IACxB;IACAC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACAC,KAAK,EAAE;MACNC,KAAK,EAAE,oBAAoB;MAC3BC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE;IACb,CAAC;IACD;IACAC,cAAc,EAAE,sBAAsB;IACtC;IACAC,KAAK,EAAE,GAAY;IACnB;IACAC,IAAI,EAAE;MACLC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IAChB,CAAC;IACD;IACAC,MAAM,EAAE;MACPf,MAAM,EAAE;IACT,CAAC;IACDgB,QAAQ,EAAE;MACTC,IAAI,EAAE;QACLzB,IAAI,EAAE;UACL,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAC/B;MACD;IACD;EACD,CAAC,CAAC,EAAE,CAACpE,EAAE,CAAC,CAAC;EAET,MAAM,CAAC8F,SAAS,EAAEC,YAAY,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMuH,SAAS,GAAGpH,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqH,YAAY,GAAGrH,MAAM,CAAwB,IAAI,CAAC;;EAExD;EACA,MAAM,CAACsH,YAAY,EAAEC,eAAe,CAAC,GAAG1H,QAAQ,CAA8C;IAAE2H,OAAO,EAAE,IAAI;IAAEC,YAAY,EAAE;EAAM,CAAC,CAAC;;EAErI;EACA,MAAMC,cAAc,GAAIC,OAAe,IAAc;IACpD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,OAAOF,WAAW,CAACG,MAAM,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAe;IAC1C,IAAIX,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEY,OAAO,EAAE;MAC1B,MAAMC,SAAS,GAAGb,YAAY,CAACY,OAAO,CAACE,aAAa,CAAC,kBAAkB,CAAC;MACxE,IAAID,SAAS,EAAE;QACd,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;MACvD;IACD;IACA,OAAO,KAAK;EACb,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIX,OAAe,IAAK;IAC/C,MAAMH,OAAO,GAAGE,cAAc,CAACC,OAAO,CAAC;IACvC,MAAMF,YAAY,GAAGO,mBAAmB,CAAC,CAAC;IAE1CT,eAAe,CAAC;MAAEC,OAAO;MAAEC;IAAa,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAGpI,WAAW,CAAEqI,UAAkB,IAAK;IAC/DnF,qBAAqB,CAACjC,EAAE,EAAEoH,UAAU,CAAC;IACrC;IACAF,kBAAkB,CAACE,UAAU,CAAC;EAC/B,CAAC,EAAE,CAACpH,EAAE,EAAEiC,qBAAqB,EAAEiF,kBAAkB,CAAC,CAAC;;EAEnD;EACAxI,SAAS,CAAC,MAAM;IACfwI,kBAAkB,CAACjH,WAAW,IAAI,EAAE,CAAC;EACtC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACAvB,SAAS,CAAC,MAAM;IACf,IAAIoH,SAAS,IAAIE,SAAS,CAACa,OAAO,EAAE;MACnCQ,UAAU,CAAC,MAAM;QACfrB,SAAS,CAACa,OAAO,CAASS,MAAM,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC,EAAE,CAACzB,SAAS,CAAC,CAAC;;EAEf;EACApH,SAAS,CAAC,MAAM;IACf,MAAM8I,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjD,MAAMC,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGhD,QAAQ,CAACqC,aAAa,CAAC,cAAc,CAAC,cAAAW,qBAAA,uBAAtCA,qBAAA,CAAwCS,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMK,kBAAkB,IAAAT,sBAAA,GAAGjD,QAAQ,CAACqC,aAAa,CAAC,gBAAgB,CAAC,cAAAY,sBAAA,uBAAxCA,sBAAA,CAA0CQ,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMM,sBAAsB,GAAGD,kBAAkB,MAAAR,sBAAA,GAAIlD,QAAQ,CAACqC,aAAa,CAAC,sBAAsB,CAAC,cAAAa,sBAAA,uBAA9CA,sBAAA,CAAgDO,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMO,iBAAiB,GAAIb,KAAK,CAACM,MAAM,CAAiB/H,EAAE,CAACuI,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAX,sBAAA,GAAGnD,QAAQ,CAACqC,aAAa,CAAC,wBAAwB,CAAC,cAAAc,sBAAA,uBAAhDA,sBAAA,CAAkDM,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMU,qBAAqB,GAAIhB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMU,cAAc,GAAIjB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,IACC/B,YAAY,CAACY,OAAO,IACpB,CAACZ,YAAY,CAACY,OAAO,CAACsB,QAAQ,CAACV,KAAK,CAACM,MAAc,CAAC;MAAI;MACxD,CAACG,aAAa;MAAI;MAClB,CAACE,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACE,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAG;MACzB,CAACC,cAAc,IACf,CAACZ,yBAAyB,IAC1B,CAACG,oBAAoB,EACpB;QACDlC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;MACtB;IACD,CAAC;IAED,IAAID,SAAS,EAAE;MACdpB,QAAQ,CAACiE,gBAAgB,CAAC,WAAW,EAAEnB,kBAAkB,CAAC;MAC1D,OAAO,MAAM9C,QAAQ,CAACkE,mBAAmB,CAAC,WAAW,EAAEpB,kBAAkB,CAAC;IAC3E;EACD,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EAEf,oBACCrG,OAAA,CAAAE,SAAA;IAAAkJ,QAAA,eACCpJ,OAAA,CAACT,GAAG;MACH8J,EAAE,EAAE;QACHC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,UAAU;QACpB;QACAC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,UAAU,EAAE,yBAAyB;QACrCpE,eAAe,EAAElD,YAAY,IAAI;QACjC;QACA;MACD,CAAE;MACFuH,SAAS,EAAC,WAAW;MACrBrJ,EAAE,EAAC,SAAS;MAAA6I,QAAA,eAGZpJ,OAAA;QACCqF,KAAK,EAAE;UACNvB,KAAK,EAAE,MAAM;UACb0F,QAAQ,EAAE,UAAU;UACpBlE,KAAK,EAAE,SAAS;UAChBC,eAAe,EAAE;QAClB,CAAE;QACFqE,SAAS,EAAC,eAAe;QAAAR,QAAA,gBAGzBpJ,OAAA;UACC4J,SAAS,EAAC,kBAAkB;UAC5BvE,KAAK,EAAE;YACNmE,QAAQ,EAAE,UAAU;YACpBK,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE,MAAM;YACb3E,MAAM,EAAE,IAAI;YACZmE,OAAO,EAAE,MAAM;YACfS,OAAO,EAAE,CAAC;YACVJ,UAAU,EAAE,0BAA0B;YACtCpE,eAAe,EAAE,0BAA0B;YAC3CyE,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,KAAK;YACdC,SAAS,EAAE;UACZ,CAAE;UAAAd,QAAA,gBAEFpJ,OAAA,CAACR,UAAU;YACV2K,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAM1H,qBAAqB,CAACnC,EAAE,CAAE;YACzC8J,QAAQ,EAAE,EAAArJ,qBAAA,GAAA6B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA5B,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCsJ,UAAU,cAAArJ,sBAAA,uBAAjDA,sBAAA,CAAmDiG,MAAM,KAAI,CAAE;YACzEqD,KAAK,EAAE,EAAArJ,sBAAA,GAAA2B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA1B,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCoJ,UAAU,cAAAnJ,sBAAA,uBAAjDA,sBAAA,CAAmD+F,MAAM,KAAI,CAAC,GAAGvF,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;YAChK0H,EAAE,EAAE;cACHvF,KAAK,EAAE,MAAM;cACbe,MAAM,EAAE,MAAM;cACdoF,OAAO,EAAE,KAAK;cACd,SAAS,EAAE;gBACV1E,eAAe,EAAE;cAClB,CAAC;cACDiF,GAAG,EAAE;gBACJ3F,MAAM,EAAE,MAAM;gBACdf,KAAK,EAAE,MAAM;gBACb2G,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAtB,QAAA,eAEFpJ,OAAA;cACC2K,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjL;cAAS,CAAE;cAC9C0F,KAAK,EAAE;gBACN0E,OAAO,EAAE,EAAA3I,sBAAA,GAAAyB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCkJ,UAAU,cAAAjJ,sBAAA,uBAAjDA,sBAAA,CAAmD6F,MAAM,KAAI,CAAC,GAAG,GAAG,GAAG,CAAC;gBACjFrC,MAAM,EAAE,MAAM;gBACdf,KAAK,EAAE,MAAM;gBACbwF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBsB,cAAc,EAAE;cACjB;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACbjL,OAAA,CAACR,UAAU;YACV2K,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAM3H,sBAAsB,CAAClC,EAAE,CAAE;YAC1C8J,QAAQ,EAAE,EAAA/I,sBAAA,GAAAuB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCgJ,UAAU,cAAA/I,sBAAA,uBAAjDA,sBAAA,CAAmD2F,MAAM,MAAK,CAAE;YAC1EqD,KAAK,EAAE5I,SAAS,CAAC,gBAAgB,CAAE;YACnC0H,EAAE,EAAE;cACHvF,KAAK,EAAE,MAAM;cACbe,MAAM,EAAE,MAAM;cACdoF,OAAO,EAAE,KAAK;cACd,SAAS,EAAE;gBACV1E,eAAe,EAAE;cAClB,CAAC;cACDiF,GAAG,EAAE;gBACJ3F,MAAM,EAAE,MAAM;gBACdf,KAAK,EAAE,MAAM;gBACb2G,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAtB,QAAA,eAEFpJ,OAAA;cACC2K,uBAAuB,EAAE;gBAAEC,MAAM,EAAEhL;cAAW,CAAE;cAChDyF,KAAK,EAAE;gBACN0E,OAAO,EAAE,EAAAvI,sBAAA,GAAAqB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAApB,sBAAA,wBAAAC,uBAAA,GAArCD,sBAAA,CAAuC8I,UAAU,cAAA7I,uBAAA,uBAAjDA,uBAAA,CAAmDyF,MAAM,MAAK,CAAC,GAAG,GAAG,GAAG,CAAC;gBAClFrC,MAAM,EAAE,MAAM;gBACdf,KAAK,EAAE,MAAM;gBACbwF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBsB,cAAc,EAAE;cACjB;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,EAGZxE,YAAY,CAACE,OAAO,iBACpB3G,OAAA,CAACR,UAAU;YACV2K,IAAI,EAAC,OAAO;YACZC,OAAO,EAAGc,CAAC,IAAK;cACfA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB7E,YAAY,CAAC,CAACD,SAAS,CAAC;YACzB,CAAE;YACFgD,EAAE,EAAE;cACHvF,KAAK,EAAE,MAAM;cACbe,MAAM,EAAE,MAAM;cACdoF,OAAO,EAAE,KAAK;cACd,SAAS,EAAE;gBACV1E,eAAe,EAAE;cAClB,CAAC;cACD,OAAO,EAAE;gBACRzB,KAAK,EAAE,MAAM;gBACbe,MAAM,EAAE;cACT;YACD,CAAE;YACF0F,KAAK,EAAE5I,SAAS,CAAC,gBAAgB,CAAE;YAAAyH,QAAA,eAEnCpJ,OAAA;cACC2K,uBAAuB,EAAE;gBAAEC,MAAM,EAAE/K;cAAS,CAAE;cAC9CwF,KAAK,EAAE;gBAAER,MAAM,EAAE,MAAM;gBAAEf,KAAK,EAAE;cAAO;YAAE;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACLjL,OAAA;UAAAoJ,QAAA,EACE;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QAAS;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACRjL,OAAA,CAACP,WAAW;UACX2L,KAAK,EAAE5K,WAAW,IAAI,EAAG;UACzB6K,MAAM,EAAEtI,WAAY;UACpBuI,QAAQ,EAAE5D;QAAoB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC,gBACL,CAAC;AAEL,CAAC;EAAA,QA7gByBnL,cAAc,EAoBnCJ,cAAc;AAAA,EA0fpB,CAAC;EAAA,QA9gB0BI,cAAc,EAoBnCJ,cAAc;AAAA,EA0fnB;AAAC6L,GAAA,GA5hBIpL,UAAqC;AA8hB3C,eAAAqL,GAAA,gBAAepM,IAAI,CAACe,UAAU,CAAC;AAAC,IAAAE,EAAA,EAAAkL,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAApL,EAAA;AAAAoL,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}