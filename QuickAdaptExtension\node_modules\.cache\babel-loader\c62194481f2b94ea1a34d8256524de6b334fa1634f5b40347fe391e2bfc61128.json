{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\AnnouncementPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Popover, Button, Typography, Box, LinearProgress, IconButton, MobileStepper } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\n\n// Helper function to get an element by XPath\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getElementByXPath = xpath => {\n  try {\n    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n    return result.singleNodeValue;\n  } catch (error) {\n    console.error(\"Error evaluating XPath:\", error);\n    return null;\n  }\n};\n\n// Helper function to convert hex color to rgba with opacity\nconst hexToRgba = (hex, opacity) => {\n  // Remove # if present\n  hex = hex.replace('#', '');\n\n  // Parse hex values\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n};\nconst AnnouncementPopup = ({\n  selectedTemplate,\n  handlecloseBannerPopup,\n  backgroundC,\n  Bposition,\n  bpadding,\n  Bbordercolor,\n  BborderSize,\n  guideStep,\n  anchorEl,\n  onClose,\n  onPrevious,\n  onContinue,\n  title,\n  text,\n  imageUrl,\n  videoUrl,\n  previousButtonLabel,\n  continueButtonLabel,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData\n}) => {\n  _s();\n  var _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide2, _savedGuideData$Guide3, _savedGuideData$Guide4, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _textFieldProperties$4, _imageProperties2, _imageProperties2$Cus, _imageProperties2$Cus2, _imageProperties3, _imageProperties3$Cus, _imageProperties3$Cus2, _imageProperties4, _imageProperties4$Cus, _imageProperties4$Cus2, _imageProperties5, _imageProperties5$Cus, _imageProperties5$Cus2, _imageProperties$, _imageProperties$$Cus, _imageProperties$$Cus2;\n  const {\n    setCurrentStep,\n    selectedOption,\n    steps,\n    setBannerPreview,\n    bannerPreview,\n    announcementPreview,\n    setAnnouncementPreview,\n    ProgressColor,\n    setProgressColor\n  } = useDrawerStore(state => state);\n  const [Overlayvalue, setOverlayValue] = useState(false);\n  const handleContinue = () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        setCurrentStep(currentStep + 1);\n        onContinue();\n      }\n    } else {\n      var _savedGuideData$Guide;\n      if (currentStep !== (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : _savedGuideData$Guide.length)) {\n        setCurrentStep(currentStep + 1);\n      }\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  // Initialize Overlayvalue state from props only once\n  useEffect(() => {\n    setOverlayValue(!!OverlayValue);\n  }, []);\n  const imageFit = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.Fit) || 'contain';\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n  const getPopoverPositionStyle = (position = \"center-center\") => {\n    // Constants\n    const EDGE_PADDING = 12; // Padding from screen edges (in px)\n\n    // Basic reset for all positioning properties\n    const baseStyle = {\n      position: 'fixed',\n      top: 'auto !important',\n      right: 'auto',\n      bottom: 'auto',\n      left: 'auto !important',\n      transform: 'none'\n    };\n\n    // Apply specific positioning based on selected position\n    switch (position) {\n      case \"top-left\":\n        return {\n          ...baseStyle,\n          top: `${EDGE_PADDING + 10}px !important`,\n          left: `${EDGE_PADDING}px !important`\n        };\n      case \"top-center\":\n        return {\n          ...baseStyle,\n          top: `${EDGE_PADDING + 10}px !important`,\n          left: '50% !important',\n          transform: 'translateX(-50%)'\n        };\n      case \"top-right\":\n        return {\n          ...baseStyle,\n          top: `${EDGE_PADDING + 10}px !important`,\n          right: `${EDGE_PADDING + 5}px`\n        };\n      // case \"left-center\":\n      case \"left-center\":\n        return {\n          ...baseStyle,\n          top: '54% !important',\n          left: `${EDGE_PADDING}px !important`,\n          transform: 'translateY(-50%)'\n        };\n      //case \"center-center\":\n      case \"center-center\":\n        return {\n          ...baseStyle,\n          top: '54% !important',\n          left: '50% !important',\n          transform: 'translate(-50%, -50%)'\n        };\n      // case \"right-center\":\n      case \"right-center\":\n        return {\n          ...baseStyle,\n          top: '54% !important',\n          right: `${EDGE_PADDING + 5}px`,\n          transform: 'translateY(-50%)'\n        };\n      case \"bottom-left\":\n        return {\n          ...baseStyle,\n          bottom: `${EDGE_PADDING}px !important`,\n          left: `${EDGE_PADDING}px !important`\n        };\n      case \"bottom-center\":\n        return {\n          ...baseStyle,\n          bottom: `${EDGE_PADDING}px`,\n          left: '50% !important',\n          transform: 'translateX(-50%)'\n        };\n      case \"bottom-right\":\n        return {\n          ...baseStyle,\n          bottom: `${EDGE_PADDING}px`,\n          right: `${EDGE_PADDING + 5}px`\n        };\n      default:\n        return {\n          ...baseStyle,\n          top: '50% !important',\n          left: '50% !important',\n          transform: 'translate(-50%, -50%)'\n        };\n    }\n  };\n  const interactWithPage = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide2 = savedGuideData.GuideStep) === null || _savedGuideData$Guide2 === void 0 ? void 0 : (_savedGuideData$Guide3 = _savedGuideData$Guide2[currentStep - 1]) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3.Tooltip) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4.InteractWithPage;\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n  const isRTL = document.body.classList.contains(\"rtl\");\n  const baseAlign = (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$4 = textFieldProperties.TextProperties) === null || _textFieldProperties$4 === void 0 ? void 0 : _textFieldProperties$4.TextFormat) || textStyle.textAlign;\n  const textAlign = isRTL ? baseAlign === \"left\" ? \"right\" : baseAlign === \"right\" ? \"left\" : baseAlign : baseAlign;\n  const imageStyle = {\n    maxHeight: ((_imageProperties2 = imageProperties[currentStep - 1]) === null || _imageProperties2 === void 0 ? void 0 : (_imageProperties2$Cus = _imageProperties2.CustomImage) === null || _imageProperties2$Cus === void 0 ? void 0 : (_imageProperties2$Cus2 = _imageProperties2$Cus[currentStep - 1]) === null || _imageProperties2$Cus2 === void 0 ? void 0 : _imageProperties2$Cus2.MaxImageHeight) || \"500px\",\n    textAlign: ((_imageProperties3 = imageProperties[currentStep - 1]) === null || _imageProperties3 === void 0 ? void 0 : (_imageProperties3$Cus = _imageProperties3.CustomImage) === null || _imageProperties3$Cus === void 0 ? void 0 : (_imageProperties3$Cus2 = _imageProperties3$Cus[currentStep - 1]) === null || _imageProperties3$Cus2 === void 0 ? void 0 : _imageProperties3$Cus2.Alignment) || \"center\",\n    objectFit: imageFit || \"contain\",\n    width: \"100%\",\n    height: `${((_imageProperties4 = imageProperties[currentStep - 1]) === null || _imageProperties4 === void 0 ? void 0 : (_imageProperties4$Cus = _imageProperties4.CustomImage) === null || _imageProperties4$Cus === void 0 ? void 0 : (_imageProperties4$Cus2 = _imageProperties4$Cus[currentStep - 1]) === null || _imageProperties4$Cus2 === void 0 ? void 0 : _imageProperties4$Cus2.SectionHeight) || 250}px`,\n    background: ((_imageProperties5 = imageProperties[currentStep - 1]) === null || _imageProperties5 === void 0 ? void 0 : (_imageProperties5$Cus = _imageProperties5.CustomImage) === null || _imageProperties5$Cus === void 0 ? void 0 : (_imageProperties5$Cus2 = _imageProperties5$Cus[currentStep - 1]) === null || _imageProperties5$Cus2 === void 0 ? void 0 : _imageProperties5$Cus2.BackgroundColor) || \"#ffffff\"\n  };\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Safely group buttons, handling potential null/undefined values\n  const groupedButtons = React.useMemo(() => {\n    if (!customButton || !Array.isArray(customButton) || customButton.length === 0) {\n      return {};\n    }\n    return customButton.reduce((acc, button) => {\n      if (!button) return acc;\n      const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n      if (!acc[containerId]) {\n        acc[containerId] = [];\n      }\n      acc[containerId].push(button);\n      return acc;\n    }, {});\n  }, [customButton]);\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: `${canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Radius ? canvasProperties.Radius : 8}px !important`,\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"transparent\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    backgroundImage: canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.BackgroundImage ? `url(${canvasProperties.BackgroundImage})` : \"none\",\n    backgroundSize: \"cover\",\n    backgroundPosition: \"center\",\n    backgroundRepeat: \"no-repeat\",\n    width: canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Width}px` : \"500px\"\n  };\n  const dissmissIconColor = \"red\";\n  const ActionButtonBackgroundcolor = \"#f0f0f0\";\n  const overlay = Overlayvalue;\n  const sectionHeight = ((_imageProperties$ = imageProperties[0]) === null || _imageProperties$ === void 0 ? void 0 : (_imageProperties$$Cus = _imageProperties$.CustomImage) === null || _imageProperties$$Cus === void 0 ? void 0 : (_imageProperties$$Cus2 = _imageProperties$$Cus[0]) === null || _imageProperties$$Cus2 === void 0 ? void 0 : _imageProperties$$Cus2.SectionHeight) || \"auto\";\n  const openInNewTab = true;\n  // Determine progress bar state based on guide type and current step\n  const enableProgress = ((_savedGuideData$Guide5, _savedGuideData$Guide6, _currentStepData$Tool, _firstStepData$Toolti) => {\n    // For AI-created announcements, check the current step's data\n    const currentStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : _savedGuideData$Guide5[currentStep - 1];\n    const firstStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide6 = savedGuideData.GuideStep) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6[0];\n    if ((currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$Tool = currentStepData.Tooltip) === null || _currentStepData$Tool === void 0 ? void 0 : _currentStepData$Tool.EnableProgress) !== undefined) {\n      return currentStepData.Tooltip.EnableProgress;\n    }\n    // Fallback to first step for backward compatibility\n    return (firstStepData === null || firstStepData === void 0 ? void 0 : (_firstStepData$Toolti = firstStepData.Tooltip) === null || _firstStepData$Toolti === void 0 ? void 0 : _firstStepData$Toolti.EnableProgress) || false;\n  })();\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[0]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.Tooltip) === null || _savedGuideData$Guide9 === void 0 ? void 0 : _savedGuideData$Guide9.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: steps.length,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"4px\"\n        },\n        children: Array.from({\n          length: steps.length\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '14px',\n            height: '4px',\n            backgroundColor: index === currentStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45),\n            // Active color and inactive color\n            borderRadius: '100px'\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 23\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 17\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", steps.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              backgroundColor: hexToRgba(ProgressColor, 0.45),\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const contentRef = useRef(null);\n  const scrollbarRef = useRef(null);\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide10, _savedGuideData$Guide11;\n        // Reset to the first step\n        setCurrentStep(1);\n        // If there's a specific URL for the first step, navigate to it\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide10 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide10 !== void 0 && (_savedGuideData$Guide11 = _savedGuideData$Guide10[0]) !== null && _savedGuideData$Guide11 !== void 0 && _savedGuideData$Guide11.ElementPath) {\n          const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    }\n  };\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const position = (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\";\n  const positionStyle = getPopoverPositionStyle(position);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [OverlayValue && selectedTemplate !== \"Tour\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        zIndex: 998,\n        pointerEvents: selectedTemplate === \"Tour\" ? \"auto\" : \"none\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"previewdata qadpt-index\",\n      open: Boolean(anchorEl),\n      anchorEl: anchorEl,\n      onClose: undefined,\n      anchorOrigin: anchorOrigin,\n      transformOrigin: transformOrigin,\n      sx: {\n        position: selectedTemplate === \"Tour\" ? \"absolute !important\" : interactWithPage && OverlayValue === false ? \"absolute !important\" : \"\",\n        zIndex: selectedTemplate === \"Tour\" ? \"auto !important\" : interactWithPage && OverlayValue === false ? \"auto !important\" : \"\",\n        \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n        \"& .MuiPaper-root:not(.MuiMobileStepper-root)\": {\n          zIndex: \"999 !important\",\n          // zIndex: 999,\n          // borderRadius: \"1px\",\n          ...canvasStyle,\n          ...positionStyle,\n          margin: \"0 !important\",\n          transform: `${positionStyle.transform} !important`,\n          overflow: \"visible\"\n        }\n      },\n      disableScrollLock: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          placeContent: \"end\",\n          display: \"flex\"\n        },\n        children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n          sx: {\n            position: \"fixed\",\n            boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n            left: \"auto\",\n            right: \"auto\",\n            margin: \"-15px\",\n            background: \"#fff !important\",\n            border: \"1px solid #ccc\",\n            zIndex: \"999999\",\n            borderRadius: \"50px\",\n            padding: \"5px !important\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            sx: {\n              zoom: \"0.7\",\n              color: \"#000\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Padding ? `${canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding}px` : \"4px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: \"550px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            style: {\n              // padding: canvasProperties?.Padding ? `${canvasProperties?.Padding}px` : \"4px\",\n              height: sectionHeight\n            },\n            ref: contentRef,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              flexWrap: \"wrap\",\n              justifyContent: \"center\",\n              children: imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map((imageProp, propIndex) => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: customImg.Url,\n                alt: customImg.AltText || \"Image\",\n                sx: {\n                  maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                  textAlign: imageProp.Alignment || \"center\",\n                  objectFit: customImg.Fit || \"contain\",\n                  background: customImg.BackgroundColor || \"#ffffff\",\n                  width: \"100%\",\n                  height: `${customImg.SectionHeight || 250}px`,\n                  //height: \"100%\",\n                  margin: 0,\n                  padding: 0,\n                  borderRadius: \"0\"\n                },\n                onClick: () => {\n                  if (imageProp.Hyperlink) {\n                    const targetUrl = imageProp.Hyperlink;\n                    window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                  }\n                },\n                style: {\n                  cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                }\n              }, `${imageProp.Id}-${imgIndex}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 10\n              }, this)))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 7\n            }, this), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n              var _textField$TextProper;\n              return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                // Use a unique key, either Id or index\n                className: \"qadpt-preview\",\n                sx: {\n                  whiteSpace: \"pre-wrap\",\n                  wordBreak: \"break-word\",\n                  textAlign,\n                  marginTop: 1,\n                  color: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextColor) || textStyle.color,\n                  padding: \"5px\"\n                },\n                dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n              }, textField.Id || index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 10\n              }, this);\n            }), Object.keys(groupedButtons).map(containerId => {\n              var _groupedButtons$conta, _groupedButtons$conta2;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                  flexWrap: \"wrap\",\n                  margin: 0,\n                  backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                  padding: \"10px\"\n                },\n                children: groupedButtons[containerId].map((button, index) => {\n                  var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6;\n                  return /*#__PURE__*/_jsxDEV(Button, {\n                    onClick: () => handleButtonAction(button.ButtonAction),\n                    variant: \"contained\",\n                    sx: {\n                      marginRight: \"13px\",\n                      margin: \"0 5px\",\n                      backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                      color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                      border: `2px solid ${(_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor}` || \"transparent\",\n                      fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"14px\",\n                      width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                      // paddingTop: \"3px\",\n                      //                         paddingRight: \"16px\",\n                      //                         paddingBottom: \"3x\",\n                      //                         paddingLeft:\"16px\",\n                      textTransform: \"none\",\n                      borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                      padding: \"var(--button-padding) !important\",\n                      lineHeight: \"var(--button-lineheight) !important\",\n                      boxShadow: \"none !important\"\n                    },\n                    children: button.ButtonName\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 10\n                  }, this);\n                })\n              }, containerId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 8\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 6\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: totalSteps >= 1 && enableProgress ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: renderProgress()\n          }, void 0, false) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 575,\n    columnNumber: 3\n  }, this);\n};\n_s(AnnouncementPopup, \"DJUkYWdo9oapef0qPa9nYsjGOAs=\", false, function () {\n  return [useDrawerStore];\n});\n_c = AnnouncementPopup;\nexport default AnnouncementPopup;\nvar _c;\n$RefreshReg$(_c, \"AnnouncementPopup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Popover", "<PERSON><PERSON>", "Typography", "Box", "LinearProgress", "IconButton", "MobileStepper", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "error", "console", "hexToRgba", "hex", "opacity", "replace", "r", "parseInt", "substring", "g", "b", "AnnouncementPopup", "selectedTemplate", "handlecloseBannerPopup", "backgroundC", "Bposition", "bpadding", "Bbordercolor", "BborderSize", "guideStep", "anchorEl", "onClose", "onPrevious", "onContinue", "title", "text", "imageUrl", "videoUrl", "previousButtonLabel", "continueButtonLabel", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "_s", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_textFieldProperties$4", "_imageProperties2", "_imageProperties2$Cus", "_imageProperties2$Cus2", "_imageProperties3", "_imageProperties3$Cus", "_imageProperties3$Cus2", "_imageProperties4", "_imageProperties4$Cus", "_imageProperties4$Cus2", "_imageProperties5", "_imageProperties5$Cus", "_imageProperties5$Cus2", "_imageProperties$", "_imageProperties$$Cus", "_imageProperties$$Cus2", "setCurrentStep", "selectedOption", "steps", "setBannerPreview", "bannerPreview", "announcementPreview", "setAnnouncementPreview", "ProgressColor", "setProgressColor", "state", "Overlayvalue", "setOverlayValue", "handleContinue", "_savedGuideData$Guide", "GuideStep", "length", "handlePrevious", "imageFit", "CustomImage", "Fit", "getAnchorAndTransformOrigins", "position", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "getPopoverPositionStyle", "EDGE_PADDING", "baseStyle", "top", "right", "bottom", "left", "transform", "interactWithPage", "<PERSON><PERSON><PERSON>", "InteractWithPage", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "isRTL", "body", "classList", "contains", "baseAlign", "TextFormat", "imageStyle", "maxHeight", "MaxImageHeight", "objectFit", "width", "height", "SectionHeight", "background", "BackgroundColor", "renderHtmlSnippet", "snippet", "__html", "match", "p1", "p2", "p3", "groupedButtons", "useMemo", "Array", "isArray", "reduce", "acc", "button", "containerId", "ContainerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "backgroundColor", "backgroundImage", "BackgroundImage", "backgroundSize", "backgroundPosition", "backgroundRepeat", "<PERSON><PERSON><PERSON>", "dissmissIconColor", "ActionButtonBackgroundcolor", "overlay", "sectionHeight", "openInNewTab", "enableProgress", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_currentStepData$Tool", "_firstStepData$Toolti", "currentStepData", "firstStepData", "EnableProgress", "undefined", "getProgressTemplate", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "style", "visibility", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "children", "from", "map", "_", "index", "value", "margin", "needsScrolling", "setNeedsScrolling", "contentRef", "scrollbarRef", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "window", "location", "href", "open", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "<PERSON>ement<PERSON><PERSON>", "firstStepElement", "scrollIntoView", "behavior", "getAlignment", "alignment", "positionStyle", "checkScrollNeeded", "current", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "zIndex", "pointerEvents", "className", "Boolean", "overflow", "disableScrollLock", "DismissOption", "boxShadow", "border", "padding", "zoom", "Padding", "ref", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "flexDirection", "flexWrap", "justifyContent", "imageProp", "propIndex", "customImg", "imgIndex", "component", "src", "Url", "alt", "AltText", "onClick", "Hyperlink", "cursor", "Id", "textField", "_textField$TextProper", "Text", "whiteSpace", "wordBreak", "marginTop", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "fontSize", "FontSize", "textTransform", "BorderRadius", "lineHeight", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/AnnouncementPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { <PERSON>over, Button, Typography, Box, LinearProgress, DialogActions,IconButton, MobileStepper } from \"@mui/material\";\r\nimport { CustomIconButton } from \"./Button\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { PopoverOrigin } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport BannerEndUser from \"../Bannerspreview/Banner\";\r\nimport BannerStepPreview from \"../tours/BannerStepPreview\"\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n// Helper function to get an element by XPath\r\nconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n    try {\r\n        const result = document.evaluate(\r\n            xpath,\r\n            document,\r\n            null,\r\n            XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n            null\r\n        );\r\n        return result.singleNodeValue as HTMLElement;\r\n    } catch (error) {\r\n        console.error(\"Error evaluating XPath:\", error);\r\n        return null;\r\n    }\r\n};\r\n\r\n// Helper function to convert hex color to rgba with opacity\r\nconst hexToRgba = (hex: string, opacity: number): string => {\r\n\t// Remove # if present\r\n\thex = hex.replace('#', '');\r\n\r\n\t// Parse hex values\r\n\tconst r = parseInt(hex.substring(0, 2), 16);\r\n\tconst g = parseInt(hex.substring(2, 4), 16);\r\n\tconst b = parseInt(hex.substring(4, 6), 16);\r\n\r\n\treturn `rgba(${r}, ${g}, ${b}, ${opacity})`;\r\n};\r\ninterface PopupProps {\r\n    handlecloseBannerPopup: any;\r\n    guideStep: any[];\r\n    anchorEl: null | HTMLElement;\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonLabel: string;\r\n    continueButtonLabel: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    backgroundC: any;\r\n    Bposition: any;\r\n    bpadding: any;\r\n    Bbordercolor: any;\r\n    BborderSize: any;\r\n    savedGuideData: GuideData | null;\r\n    selectedTemplate:any\r\n    ProgressColor:any}\r\n\r\nconst AnnouncementPopup: React.FC<PopupProps> = ({\r\n    selectedTemplate,\r\n    handlecloseBannerPopup,\r\n    backgroundC,\r\n    Bposition,\r\n    bpadding,\r\n    Bbordercolor,\r\n    BborderSize,\r\n    guideStep,\r\n    anchorEl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    videoUrl,\r\n    previousButtonLabel,\r\n    continueButtonLabel,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData\r\n}) => {\r\n\r\n    const {\r\n        setCurrentStep,\r\n        selectedOption,\r\n        steps,\r\n        setBannerPreview,\r\n        bannerPreview,\r\n        announcementPreview, setAnnouncementPreview,\r\n        ProgressColor,\r\n\t\tsetProgressColor\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n    const [Overlayvalue, setOverlayValue] = useState(false);\r\n    const handleContinue = () => {\r\n        if (selectedTemplate !== \"Tour\") {\r\n            if (currentStep < totalSteps) {\r\n                setCurrentStep(currentStep + 1);\r\n                onContinue();\r\n            }\r\n        }\r\n        else {\r\n            if (currentStep !== savedGuideData?.GuideStep?.length) {\r\n                setCurrentStep(currentStep + 1);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handlePrevious = () => {\r\n        if (currentStep > 1) {\r\n            setCurrentStep(currentStep - 1);\r\n            onPrevious();\r\n        }\r\n    };\r\n    // Initialize Overlayvalue state from props only once\r\n    useEffect(() => {\r\n        setOverlayValue(!!OverlayValue);\r\n    }, []);\r\n    const imageFit = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || 'contain';\r\n    const getAnchorAndTransformOrigins = (position: string): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n        switch (position) {\r\n            case \"top-left\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"left\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"right\" } };\r\n            case \"top-right\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"right\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"left\" } };\r\n            case \"bottom-left\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"left\" }, transformOrigin: { vertical: \"top\", horizontal: \"right\" } };\r\n            case \"bottom-right\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"right\" }, transformOrigin: { vertical: \"center\", horizontal: \"left\" } };\r\n            case \"center-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n            case \"top-center\":\r\n                return { anchorOrigin: { vertical: \"top\", horizontal: \"center\" }, transformOrigin: { vertical: \"bottom\", horizontal: \"center\" } };\r\n            case \"left-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"left\" }, transformOrigin: { vertical: \"center\", horizontal: \"right\" } };\r\n            case \"bottom-center\":\r\n                return { anchorOrigin: { vertical: \"bottom\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n            case \"right-center\":\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"right\" }, transformOrigin: { vertical: \"center\", horizontal: \"left\" } };\r\n            default:\r\n                return { anchorOrigin: { vertical: \"center\", horizontal: \"center\" }, transformOrigin: { vertical: \"center\", horizontal: \"center\" } };\r\n        }\r\n    };\r\n    const getPopoverPositionStyle = (position: string = \"center-center\") => {\r\n        // Constants\r\n        const EDGE_PADDING = 12; // Padding from screen edges (in px)\r\n\r\n        // Basic reset for all positioning properties\r\n        const baseStyle = {\r\n            position: 'fixed',\r\n            top: 'auto !important',\r\n            right: 'auto',\r\n            bottom: 'auto',\r\n            left: 'auto !important',\r\n            transform: 'none'\r\n        };\r\n\r\n         // Apply specific positioning based on selected position\r\n         switch (position) {\r\n            case \"top-left\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10 }px !important`,\r\n                    left: `${EDGE_PADDING}px !important`\r\n                };\r\n            case \"top-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10}px !important`,\r\n                    left: '50% !important',\r\n                    transform: 'translateX(-50%)'\r\n                };\r\n            case \"top-right\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: `${EDGE_PADDING + 10 }px !important`,\r\n                    right: `${EDGE_PADDING + 5}px`\r\n                };\r\n           // case \"left-center\":\r\n            case \"left-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    left: `${EDGE_PADDING}px !important`,\r\n                    transform: 'translateY(-50%)'\r\n                };\r\n            //case \"center-center\":\r\n            case \"center-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    left: '50% !important',\r\n                    transform: 'translate(-50%, -50%)'\r\n                };\r\n           // case \"right-center\":\r\n            case \"right-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '54% !important',\r\n                    right: `${EDGE_PADDING + 5}px`,\r\n                    transform: 'translateY(-50%)'\r\n                };\r\n            case \"bottom-left\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING }px !important`,\r\n                    left: `${EDGE_PADDING}px !important`\r\n                };\r\n            case \"bottom-center\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING}px`,\r\n                    left: '50% !important',\r\n                    transform: 'translateX(-50%)'\r\n                };\r\n            case \"bottom-right\":\r\n                return {\r\n                    ...baseStyle,\r\n                    bottom: `${EDGE_PADDING }px`,\r\n                    right: `${EDGE_PADDING + 5}px`\r\n                };\r\n            default:\r\n                return {\r\n                    ...baseStyle,\r\n                    top: '50% !important',\r\n                    left: '50% !important',\r\n                    transform: 'translate(-50%, -50%)'\r\n                };\r\n        }\r\n    };\r\n\r\n    const interactWithPage = savedGuideData?.GuideStep?.[currentStep - 1]?.Tooltip?.InteractWithPage;\r\n    const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n    const textStyle = {\r\n        fontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n        fontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n        color: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n        textAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\tconst isRTL = document.body.classList.contains(\"rtl\");\r\n\r\nconst baseAlign = textFieldProperties?.TextProperties?.TextFormat || textStyle.textAlign;\r\n\r\nconst textAlign = isRTL\r\n  ? baseAlign === \"left\"\r\n    ? \"right\"\r\n    : baseAlign === \"right\"\r\n    ? \"left\"\r\n    : baseAlign\r\n  : baseAlign;\r\n\r\n\r\n    const imageStyle = {\r\n        maxHeight: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.MaxImageHeight || \"500px\",\r\n        textAlign: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Alignment || \"center\",\r\n        objectFit: imageFit || \"contain\",\r\n        width: \"100%\",\r\n        height: `${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`,\r\n        background: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || \"#ffffff\",\r\n    };\r\n\r\n    const renderHtmlSnippet = (snippet: string) => {\r\n        // Return the raw HTML snippet for rendering\r\n        return { __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n            return `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n        }) };\r\n    };\r\n\r\n    // Safely group buttons, handling potential null/undefined values\r\n    const groupedButtons = React.useMemo(() => {\r\n        if (!customButton || !Array.isArray(customButton) || customButton.length === 0) {\r\n            return {};\r\n        }\r\n\r\n        return customButton.reduce((acc: any, button: any) => {\r\n            if (!button) return acc;\r\n\r\n        const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n        if (!acc[containerId]) {\r\n          acc[containerId] = [];\r\n        }\r\n        acc[containerId].push(button);\r\n        return acc;\r\n      }, {});\r\n    }, [customButton]);\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px !important`,\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"transparent\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tbackgroundImage: canvasProperties?.BackgroundImage ? `url(${canvasProperties.BackgroundImage})` : \"none\",\r\n\t\tbackgroundSize: \"cover\",\r\n\t\tbackgroundPosition: \"center\",\r\n\t\tbackgroundRepeat: \"no-repeat\",\r\n\t\twidth: canvasProperties?.Width ? `${canvasProperties?.Width}px` : \"500px\",\r\n\t};\r\n\tconst dissmissIconColor = \"red\";\r\n\tconst ActionButtonBackgroundcolor = \"#f0f0f0\";\r\n\tconst overlay: boolean = Overlayvalue;\r\n\tconst sectionHeight = imageProperties[0]?.CustomImage?.[0]?.SectionHeight || \"auto\";\r\n\tconst openInNewTab = true;\r\n\t// Determine progress bar state based on guide type and current step\r\n\tconst enableProgress = (() => {\r\n\t\t// For AI-created announcements, check the current step's data\r\n\t\tconst currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n\t\tconst firstStepData = savedGuideData?.GuideStep?.[0];\r\n\r\n\t\tif (currentStepData?.Tooltip?.EnableProgress !== undefined) {\r\n\t\t\treturn currentStepData.Tooltip.EnableProgress;\r\n\t\t}\r\n\t\t// Fallback to first step for backward compatibility\r\n\t\treturn firstStepData?.Tooltip?.EnableProgress || false;\r\n\t})();\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n        if (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"4px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: steps.length }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\"\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{color: ProgressColor}}>\r\n                    Step {currentStep} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box >\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n                            sx={{\r\n                                height: \"6px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: hexToRgba(ProgressColor, 0.45),\r\n                                '& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n    };\r\n    // State to track if scrolling is needed\r\n    const [needsScrolling, setNeedsScrolling] = useState(false);\r\n    const contentRef = useRef<HTMLDivElement>(null);\r\n  const scrollbarRef = useRef<any>(null);\r\n    const handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action===\"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"previous\") {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\r\n    const position = canvasProperties?.Position || \"center-center\";\r\n    const positionStyle = getPopoverPositionStyle(position);\r\n    // Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{OverlayValue && selectedTemplate!==\"Tour\"&&(\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\tzIndex: 998,\r\n\t\t\t\t\t\tpointerEvents: selectedTemplate === \"Tour\" ? \"auto\" : \"none\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t<Popover\r\n\t\t\t\tclassName=\"previewdata qadpt-index\"\r\n\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tonClose={undefined}\r\n\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tposition:selectedTemplate===\"Tour\"?\"absolute !important\": interactWithPage && OverlayValue === false ? \"absolute !important\" : \"\",\r\n\t\t\t\t\tzIndex:selectedTemplate===\"Tour\"?\"auto !important\": interactWithPage && OverlayValue === false ? \"auto !important\" : \"\",\r\n\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\"& .MuiPaper-root:not(.MuiMobileStepper-root)\": {\r\n\t\t\t\t\t\tzIndex: \"999 !important\",\r\n\t\t\t\t\t\t// zIndex: 999,\r\n\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t...positionStyle,\r\n                        margin: \"0 !important\",\r\n\t\t\t\t\t\ttransform: `${positionStyle.transform} !important`,\r\n\r\n\t\t\t\t\t\toverflow: \"visible\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\tdisableScrollLock={true}\r\n\t\t\t>\r\n\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: \"0.7\", color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div style={{padding: canvasProperties?.Padding ? `${canvasProperties?.Padding}px` : \"4px\",\r\n}}>\r\n                <PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"550px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t// padding: canvasProperties?.Padding ? `${canvasProperties?.Padding}px` : \"4px\",\r\n\t\t\t\t\t\t\theight: sectionHeight,\r\n                        }}\r\n                        ref={contentRef}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any, propIndex: number) =>\r\n\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t//height: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: 0,\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign,\r\n\t\t\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding:\"5px\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n           padding: \"10px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: `2px solid ${button.ButtonProperties?.ButtonBorderColor}` || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t// paddingTop: \"3px\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingRight: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingBottom: \"3x\",\r\n\t\t\t\t\t\t\t\t\t\t\t//                         paddingLeft:\"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"var(--button-padding) !important\",\r\n                                            lineHeight: \"var(--button-lineheight) !important\",\r\n                                            boxShadow: \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\r\n               </PerfectScrollbar>\r\n                <div>\r\n\t\t\t\t\t\t{/* Render Step Progress */}\r\n                        {totalSteps >= 1 && enableProgress ? <>{renderProgress()}</> : null}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t</Popover>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default AnnouncementPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,GAAG,EAAEC,cAAc,EAAgBC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AAEzH,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAOC,cAAc,MAAuB,yBAAyB;AAIrE,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;;AAGpD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;EAC7D,IAAI;IACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAC5BH,KAAK,EACLE,QAAQ,EACR,IAAI,EACJE,WAAW,CAACC,uBAAuB,EACnC,IACJ,CAAC;IACD,OAAOJ,MAAM,CAACK,eAAe;EACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,OAAO,IAAI;EACf;AACJ,CAAC;;AAED;AACA,MAAME,SAAS,GAAGA,CAACC,GAAW,EAAEC,OAAe,KAAa;EAC3D;EACAD,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;;EAE1B;EACA,MAAMC,CAAC,GAAGC,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3C,MAAMC,CAAC,GAAGF,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3C,MAAME,CAAC,GAAGH,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAE3C,OAAO,QAAQF,CAAC,KAAKG,CAAC,KAAKC,CAAC,KAAKN,OAAO,GAAG;AAC5C,CAAC;AAoDD,MAAMO,iBAAuC,GAAGA,CAAC;EAC7CC,gBAAgB;EAChBC,sBAAsB;EACtBC,WAAW;EACXC,SAAS;EACTC,QAAQ;EACRC,YAAY;EACZC,WAAW;EACXC,SAAS;EACTC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,mBAAmB;EACnBC,mBAAmB;EACnBC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC;AACJ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAEF,MAAM;IACFC,cAAc;IACdC,cAAc;IACdC,KAAK;IACLC,gBAAgB;IAChBC,aAAa;IACbC,mBAAmB;IAAEC,sBAAsB;IAC3CC,aAAa;IACnBC;EACD,CAAC,GAAG5F,cAAc,CAAE6F,KAAkB,IAAKA,KAAK,CAAC;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM2G,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAItE,gBAAgB,KAAK,MAAM,EAAE;MAC7B,IAAIkB,WAAW,GAAGC,UAAU,EAAE;QAC1BuC,cAAc,CAACxC,WAAW,GAAG,CAAC,CAAC;QAC/BP,UAAU,CAAC,CAAC;MAChB;IACJ,CAAC,MACI;MAAA,IAAA4D,qBAAA;MACD,IAAIrD,WAAW,MAAKa,cAAc,aAAdA,cAAc,wBAAAwC,qBAAA,GAAdxC,cAAc,CAAEyC,SAAS,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BE,MAAM,GAAE;QACnDf,cAAc,CAACxC,WAAW,GAAG,CAAC,CAAC;MACnC;IACJ;EACJ,CAAC;EAED,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIxD,WAAW,GAAG,CAAC,EAAE;MACjBwC,cAAc,CAACxC,WAAW,GAAG,CAAC,CAAC;MAC/BR,UAAU,CAAC,CAAC;IAChB;EACJ,CAAC;EACD;EACA9C,SAAS,CAAC,MAAM;IACZyG,eAAe,CAAC,CAAC,CAACvC,YAAY,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EACN,MAAM6C,QAAQ,GAAG,EAAA1C,gBAAA,GAAAV,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAe,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkC2C,WAAW,cAAA1C,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgDhB,WAAW,GAAG,CAAC,CAAC,cAAAiB,sBAAA,uBAAhEA,sBAAA,CAAkE0C,GAAG,KAAI,SAAS;EACnG,MAAMC,4BAA4B,GAAIC,QAAgB,IAAsE;IACxH,QAAQA,QAAQ;MACZ,KAAK,UAAU;QACX,OAAO;UAAEC,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAAE,CAAC;MAClI,KAAK,WAAW;QACZ,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAAE,CAAC;MAClI,KAAK,aAAa;QACd,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QAAE,CAAC;MAClI,KAAK,cAAc;QACf,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAAE,CAAC;MACrI,KAAK,eAAe;QAChB,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAAE,CAAC;MACxI,KAAK,YAAY;QACb,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAAE,CAAC;MACrI,KAAK,aAAa;QACd,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAAE,CAAC;MACrI,KAAK,eAAe;QAChB,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAAE,CAAC;MACxI,KAAK,cAAc;QACf,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAAE,CAAC;MACrI;QACI,OAAO;UAAEF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAAEC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAAE,CAAC;IAC5I;EACJ,CAAC;EACD,MAAME,uBAAuB,GAAGA,CAACL,QAAgB,GAAG,eAAe,KAAK;IACpE;IACA,MAAMM,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAMC,SAAS,GAAG;MACdP,QAAQ,EAAE,OAAO;MACjBQ,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAE;IACf,CAAC;;IAEA;IACA,QAAQZ,QAAQ;MACb,KAAK,UAAU;QACX,OAAO;UACH,GAAGO,SAAS;UACZC,GAAG,EAAE,GAAGF,YAAY,GAAG,EAAE,eAAgB;UACzCK,IAAI,EAAE,GAAGL,YAAY;QACzB,CAAC;MACL,KAAK,YAAY;QACb,OAAO;UACH,GAAGC,SAAS;UACZC,GAAG,EAAE,GAAGF,YAAY,GAAG,EAAE,eAAe;UACxCK,IAAI,EAAE,gBAAgB;UACtBC,SAAS,EAAE;QACf,CAAC;MACL,KAAK,WAAW;QACZ,OAAO;UACH,GAAGL,SAAS;UACZC,GAAG,EAAE,GAAGF,YAAY,GAAG,EAAE,eAAgB;UACzCG,KAAK,EAAE,GAAGH,YAAY,GAAG,CAAC;QAC9B,CAAC;MACN;MACC,KAAK,aAAa;QACd,OAAO;UACH,GAAGC,SAAS;UACZC,GAAG,EAAE,gBAAgB;UACrBG,IAAI,EAAE,GAAGL,YAAY,eAAe;UACpCM,SAAS,EAAE;QACf,CAAC;MACL;MACA,KAAK,eAAe;QAChB,OAAO;UACH,GAAGL,SAAS;UACZC,GAAG,EAAE,gBAAgB;UACrBG,IAAI,EAAE,gBAAgB;UACtBC,SAAS,EAAE;QACf,CAAC;MACN;MACC,KAAK,cAAc;QACf,OAAO;UACH,GAAGL,SAAS;UACZC,GAAG,EAAE,gBAAgB;UACrBC,KAAK,EAAE,GAAGH,YAAY,GAAG,CAAC,IAAI;UAC9BM,SAAS,EAAE;QACf,CAAC;MACL,KAAK,aAAa;QACd,OAAO;UACH,GAAGL,SAAS;UACZG,MAAM,EAAE,GAAGJ,YAAY,eAAgB;UACvCK,IAAI,EAAE,GAAGL,YAAY;QACzB,CAAC;MACL,KAAK,eAAe;QAChB,OAAO;UACH,GAAGC,SAAS;UACZG,MAAM,EAAE,GAAGJ,YAAY,IAAI;UAC3BK,IAAI,EAAE,gBAAgB;UACtBC,SAAS,EAAE;QACf,CAAC;MACL,KAAK,cAAc;QACf,OAAO;UACH,GAAGL,SAAS;UACZG,MAAM,EAAE,GAAGJ,YAAY,IAAK;UAC5BG,KAAK,EAAE,GAAGH,YAAY,GAAG,CAAC;QAC9B,CAAC;MACL;QACI,OAAO;UACH,GAAGC,SAAS;UACZC,GAAG,EAAE,gBAAgB;UACrBG,IAAI,EAAE,gBAAgB;UACtBC,SAAS,EAAE;QACf,CAAC;IACT;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAG7D,cAAc,aAAdA,cAAc,wBAAAK,sBAAA,GAAdL,cAAc,CAAEyC,SAAS,cAAApC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BlB,WAAW,GAAG,CAAC,CAAC,cAAAmB,sBAAA,wBAAAC,sBAAA,GAA5CD,sBAAA,CAA8CwD,OAAO,cAAAvD,sBAAA,uBAArDA,sBAAA,CAAuDwD,gBAAgB;EAChG,MAAM;IAAEd,YAAY;IAAEG;EAAgB,CAAC,GAAGL,4BAA4B,CAAC,CAAApD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE,QAAQ,KAAI,eAAe,CAAC;EAErH,MAAMC,SAAS,GAAG;IACdC,UAAU,EAAE3E,mBAAmB,aAAnBA,mBAAmB,gBAAAiB,qBAAA,GAAnBjB,mBAAmB,CAAE4E,cAAc,cAAA3D,qBAAA,eAAnCA,qBAAA,CAAqC4D,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAE9E,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,sBAAA,GAAnBlB,mBAAmB,CAAE4E,cAAc,cAAA1D,sBAAA,eAAnCA,sBAAA,CAAqC6D,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAAhF,mBAAmB,aAAnBA,mBAAmB,wBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAE4E,cAAc,cAAAzD,sBAAA,uBAAnCA,sBAAA,CAAqC8D,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAAlF,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEmF,SAAS,KAAI;EACpD,CAAC;EACD,MAAMC,KAAK,GAAG3H,QAAQ,CAAC4H,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;EAEtD,MAAMC,SAAS,GAAG,CAAAxF,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAE4E,cAAc,cAAAxD,sBAAA,uBAAnCA,sBAAA,CAAqCqE,UAAU,KAAIf,SAAS,CAACQ,SAAS;EAExF,MAAMA,SAAS,GAAGE,KAAK,GACnBI,SAAS,KAAK,MAAM,GAClB,OAAO,GACPA,SAAS,KAAK,OAAO,GACrB,MAAM,GACNA,SAAS,GACXA,SAAS;EAGT,MAAME,UAAU,GAAG;IACfC,SAAS,EAAE,EAAAtE,iBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,iBAAA,wBAAAC,qBAAA,GAAhCD,iBAAA,CAAkCiC,WAAW,cAAAhC,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkEqE,cAAc,KAAI,OAAO;IACtGV,SAAS,EAAE,EAAA1D,iBAAA,GAAAvB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAA4B,iBAAA,wBAAAC,qBAAA,GAAhCD,iBAAA,CAAkC8B,WAAW,cAAA7B,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD7B,WAAW,GAAG,CAAC,CAAC,cAAA8B,sBAAA,uBAAhEA,sBAAA,CAAkEyD,SAAS,KAAI,QAAQ;IAClGU,SAAS,EAAExC,QAAQ,IAAI,SAAS;IAChCyC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,GAAG,EAAApE,iBAAA,GAAA1B,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAA+B,iBAAA,wBAAAC,qBAAA,GAAhCD,iBAAA,CAAkC2B,WAAW,cAAA1B,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgDhC,WAAW,GAAG,CAAC,CAAC,cAAAiC,sBAAA,uBAAhEA,sBAAA,CAAkEmE,aAAa,KAAI,GAAG,IAAI;IACrGC,UAAU,EAAE,EAAAnE,iBAAA,GAAA7B,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAkC,iBAAA,wBAAAC,qBAAA,GAAhCD,iBAAA,CAAkCwB,WAAW,cAAAvB,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgDnC,WAAW,GAAG,CAAC,CAAC,cAAAoC,sBAAA,uBAAhEA,sBAAA,CAAkEkE,eAAe,KAAI;EACrG,CAAC;EAED,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC3C;IACA,OAAO;MAAEC,MAAM,EAAED,OAAO,CAACjI,OAAO,CAAC,qCAAqC,EAAE,CAACmI,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QAC3F,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC7C,CAAC;IAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGtK,KAAK,CAACuK,OAAO,CAAC,MAAM;IACvC,IAAI,CAACzG,YAAY,IAAI,CAAC0G,KAAK,CAACC,OAAO,CAAC3G,YAAY,CAAC,IAAIA,YAAY,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC5E,OAAO,CAAC,CAAC;IACb;IAEA,OAAOjD,YAAY,CAAC4G,MAAM,CAAC,CAACC,GAAQ,EAAEC,MAAW,KAAK;MAClD,IAAI,CAACA,MAAM,EAAE,OAAOD,GAAG;MAE3B,MAAME,WAAW,GAAGD,MAAM,CAACE,WAAW,IAAI,SAAS,CAAC,CAAC;MACrD,IAAI,CAACH,GAAG,CAACE,WAAW,CAAC,EAAE;QACrBF,GAAG,CAACE,WAAW,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACE,WAAW,CAAC,CAACE,IAAI,CAACH,MAAM,CAAC;MAC7B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAAC7G,YAAY,CAAC,CAAC;EAErB,MAAMkH,WAAW,GAAG;IACnB3D,QAAQ,EAAE,CAAArD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE,QAAQ,KAAI,eAAe;IACvD4C,YAAY,EAAE,GAAGjH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkH,MAAM,GAAGlH,gBAAgB,CAACkH,MAAM,GAAG,CAAC,eAAe;IACtFC,WAAW,EAAE,CAAAnH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoH,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAArH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsH,WAAW,KAAI,aAAa;IAC3DC,WAAW,EAAE,OAAO;IACpBC,eAAe,EAAE,CAAAxH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8F,eAAe,KAAI,OAAO;IAC7D2B,eAAe,EAAEzH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE0H,eAAe,GAAG,OAAO1H,gBAAgB,CAAC0H,eAAe,GAAG,GAAG,MAAM;IACxGC,cAAc,EAAE,OAAO;IACvBC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,WAAW;IAC7BnC,KAAK,EAAE1F,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE8H,KAAK,GAAG,GAAG9H,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8H,KAAK,IAAI,GAAG;EACnE,CAAC;EACD,MAAMC,iBAAiB,GAAG,KAAK;EAC/B,MAAMC,2BAA2B,GAAG,SAAS;EAC7C,MAAMC,OAAgB,GAAGvF,YAAY;EACrC,MAAMwF,aAAa,GAAG,EAAArG,iBAAA,GAAAhC,eAAe,CAAC,CAAC,CAAC,cAAAgC,iBAAA,wBAAAC,qBAAA,GAAlBD,iBAAA,CAAoBqB,WAAW,cAAApB,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsC6D,aAAa,KAAI,MAAM;EACnF,MAAMuC,YAAY,GAAG,IAAI;EACzB;EACA,MAAMC,cAAc,GAAG,CAAC,CAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,KAAM;IAC7B;IACA,MAAMC,eAAe,GAAGpI,cAAc,aAAdA,cAAc,wBAAAgI,sBAAA,GAAdhI,cAAc,CAAEyC,SAAS,cAAAuF,sBAAA,uBAAzBA,sBAAA,CAA4B7I,WAAW,GAAG,CAAC,CAAC;IACpE,MAAMkJ,aAAa,GAAGrI,cAAc,aAAdA,cAAc,wBAAAiI,sBAAA,GAAdjI,cAAc,CAAEyC,SAAS,cAAAwF,sBAAA,uBAAzBA,sBAAA,CAA4B,CAAC,CAAC;IAEpD,IAAI,CAAAG,eAAe,aAAfA,eAAe,wBAAAF,qBAAA,GAAfE,eAAe,CAAEtE,OAAO,cAAAoE,qBAAA,uBAAxBA,qBAAA,CAA0BI,cAAc,MAAKC,SAAS,EAAE;MAC3D,OAAOH,eAAe,CAACtE,OAAO,CAACwE,cAAc;IAC9C;IACA;IACA,OAAO,CAAAD,aAAa,aAAbA,aAAa,wBAAAF,qBAAA,GAAbE,aAAa,CAAEvE,OAAO,cAAAqE,qBAAA,uBAAtBA,qBAAA,CAAwBG,cAAc,KAAI,KAAK;EACvD,CAAC,EAAE,CAAC;EAAE,SAASE,mBAAmBA,CAAC5G,cAAmB,EAAE;IAAA,IAAA6G,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvD,IAAI/G,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MACU,IAAIA,cAAc,KAAK,CAAC,EAAE;MACpC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA5B,cAAc,aAAdA,cAAc,wBAAAyI,sBAAA,GAAdzI,cAAc,CAAEyC,SAAS,cAAAgG,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC5E,OAAO,cAAA6E,sBAAA,uBAAvCA,sBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAAC5G,cAAc,CAAC;EAC5D,MAAMkH,cAAc,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACf,cAAc,EAAE,OAAO,IAAI;IAEtC,IAAIc,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCnM,OAAA,CAACL,aAAa;QACb0M,OAAO,EAAC,MAAM;QACdlH,KAAK,EAAEA,KAAK,CAACa,MAAO;QACpBM,QAAQ,EAAC,QAAQ;QACjBgG,UAAU,EAAE7J,WAAW,GAAG,CAAE;QAC5B8J,EAAE,EAAE;UAAE9B,eAAe,EAAE,aAAa;UAAG,+BAA+B,EAAE;YACrDA,eAAe,EAAEjF,aAAa,CAAE;UAClC;QAAG,CAAE;QACtBgH,UAAU,eAAExM,OAAA,CAACV,MAAM;UAACmN,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDC,UAAU,eAAE/M,OAAA,CAACV,MAAM;UAACmN,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACM,IAAIX,gBAAgB,KAAK,aAAa,EAAE;MAC7C,oBACanM,OAAA,CAACR,GAAG;QAAC+M,EAAE,EAAE;UAACS,OAAO,EAAE,MAAM;UACpCC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,QAAQ;UACtBC,GAAG,EAAE;QAAK,CAAE;QAAAC,QAAA,EAGI3D,KAAK,CAAC4D,IAAI,CAAC;UAAErH,MAAM,EAAEb,KAAK,CAACa;QAAO,CAAC,CAAC,CAACsH,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACjDxN,OAAA;UAEEyM,KAAK,EAAE;YACL9D,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACb6B,eAAe,EAAE+C,KAAK,KAAK/K,WAAW,GAAG,CAAC,GAAG+C,aAAa,GAAG3E,SAAS,CAAC2E,aAAa,EAAE,IAAI,CAAC;YAAE;YAC7F0E,YAAY,EAAE;UAChB;QAAE,GANGsD,KAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAEpB;IACA,IAAIX,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCnM,OAAA,CAACR,GAAG;QAAC+M,EAAE,EAAE;UAACS,OAAO,EAAE,MAAM;UACzBC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QACd,CAAE;QAAAE,QAAA,eACDpN,OAAA,CAACT,UAAU;UAACgN,EAAE,EAAE;YAAC1E,KAAK,EAAErC;UAAa,CAAE;UAAA4H,QAAA,GAAC,OACpB,EAAC3K,WAAW,EAAC,MAAI,EAAC0C,KAAK,CAACa,MAAM;QAAA;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAIX,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCnM,OAAA,CAACR,GAAG;QAAA4N,QAAA,eACHpN,OAAA,CAACT,UAAU;UAAC8M,OAAO,EAAC,OAAO;UAAAe,QAAA,eAC1BpN,OAAA,CAACP,cAAc;YACd4M,OAAO,EAAC,aAAa;YACrBoB,KAAK,EAAE7K,QAAS;YACK2J,EAAE,EAAE;cACA3D,MAAM,EAAE,KAAK;cACrCsB,YAAY,EAAE,MAAM;cACpBwD,MAAM,EAAE,UAAU;cAClBjD,eAAe,EAAE5J,SAAS,CAAC2E,aAAa,EAAE,IAAI,CAAC;cACvB,0BAA0B,EAAE;gBAC5BiF,eAAe,EAAEjF,aAAa,CAAE;cAClC;YAAE;UAAE;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACT,CAAC;EACD;EACA,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAG1O,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM2O,UAAU,GAAGzO,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAM0O,YAAY,GAAG1O,MAAM,CAAM,IAAI,CAAC;EACpC,MAAM2O,kBAAkB,GAAIC,MAAW,IAAK;IAC9C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAG,SAAS,EAAE;MAC1F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGL,SAAS;MACjC,CAAC,MAAM;QACN;QACAG,MAAM,CAACG,IAAI,CAACN,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IAAIF,MAAM,CAACC,MAAM,IAAI,UAAU,IAAID,MAAM,CAACC,MAAM,IAAI,UAAU,IAAID,MAAM,CAACI,WAAW,IAAI,UAAU,IAAIJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAAE;QACvInI,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IAAI+H,MAAM,CAACC,MAAM,IAAI,MAAM,IAAID,MAAM,CAACC,MAAM,IAAI,MAAM,IAAID,MAAM,CAACI,WAAW,IAAI,MAAM,IAAIJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAAE;QAC9HvI,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IAAImI,MAAM,CAACC,MAAM,IAAI,SAAS,IAAID,MAAM,CAACI,WAAW,IAAI,SAAS,EAAE;QAAA,IAAAK,uBAAA,EAAAC,uBAAA;QACzE;QACAzJ,cAAc,CAAC,CAAC,CAAC;QACjB;QACA,IAAI3B,cAAc,aAAdA,cAAc,gBAAAmL,uBAAA,GAAdnL,cAAc,CAAEyC,SAAS,cAAA0I,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCC,WAAW,EAAE;UAChD,MAAMC,gBAAgB,GAAGzO,iBAAiB,CAACmD,cAAc,CAACyC,SAAS,CAAC,CAAC,CAAC,CAAC4I,WAAW,CAAC;UACnF,IAAIC,gBAAgB,EAAE;YACrBA,gBAAgB,CAACC,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACxD;QACD;MACD;IACD;EACD,CAAC;EACD,SAASC,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EAEG,MAAM1I,QAAQ,GAAG,CAAArD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE,QAAQ,KAAI,eAAe;EAC9D,MAAM2H,aAAa,GAAGtI,uBAAuB,CAACL,QAAQ,CAAC;EACvD;EACHnH,SAAS,CAAC,MAAM;IACf,MAAM+P,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIrB,UAAU,CAACsB,OAAO,EAAE;QACvB;QACAtB,UAAU,CAACsB,OAAO,CAAC1C,KAAK,CAAC7D,MAAM,GAAG,MAAM;QACxC,MAAMwG,aAAa,GAAGvB,UAAU,CAACsB,OAAO,CAACE,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpD1B,iBAAiB,CAAC2B,YAAY,CAAC;;QAE/B;QACA,IAAIzB,YAAY,CAACqB,OAAO,EAAE;UACzB;UACA,IAAIrB,YAAY,CAACqB,OAAO,CAACK,YAAY,EAAE;YACtC1B,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAC,CAAC;UACpC;UACA;UACAC,UAAU,CAAC,MAAM;YAChB,IAAI3B,YAAY,CAACqB,OAAO,IAAIrB,YAAY,CAACqB,OAAO,CAACK,YAAY,EAAE;cAC9D1B,YAAY,CAACqB,OAAO,CAACK,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDN,iBAAiB,CAAC,CAAC;IAGnB,MAAMQ,QAAQ,GAAG,CAChBD,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC,EACjCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,EAClCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,EAClCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIS,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAI/B,UAAU,CAACsB,OAAO,IAAId,MAAM,CAACwB,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCJ,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,cAAc,CAACG,OAAO,CAACjC,UAAU,CAACsB,OAAO,CAAC;IAC3C;IAGA,IAAItB,UAAU,CAACsB,OAAO,IAAId,MAAM,CAAC0B,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CN,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFU,gBAAgB,CAACE,OAAO,CAACjC,UAAU,CAACsB,OAAO,EAAE;QAC5Ca,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC;MAC9B,IAAIV,cAAc,EAAE;QACnBA,cAAc,CAACW,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIV,gBAAgB,EAAE;QACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAC7N,WAAW,CAAC,CAAC;EACjB,oBACCzC,OAAA;IAAAoN,QAAA,GACE/J,YAAY,IAAI9B,gBAAgB,KAAG,MAAM,iBACzCvB,OAAA;MACCyM,KAAK,EAAE;QACNnG,QAAQ,EAAE,OAAO;QACjBQ,GAAG,EAAE,CAAC;QACNG,IAAI,EAAE,CAAC;QACPF,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTyD,eAAe,EAAE,oBAAoB;QACrC8F,MAAM,EAAE,GAAG;QACXC,aAAa,EAAEjP,gBAAgB,KAAK,MAAM,GAAG,MAAM,GAAG;MACvD;IAAE;MAAAoL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACD,eACD9M,OAAA,CAACX,OAAO;MACPoR,SAAS,EAAC,yBAAyB;MACnCjC,IAAI,EAAEkC,OAAO,CAAC3O,QAAQ,CAAE;MACxBA,QAAQ,EAAEA,QAAS;MACnBC,OAAO,EAAE6J,SAAU;MACnBtF,YAAY,EAAEA,YAAa;MAC3BG,eAAe,EAAEA,eAAgB;MACjC6F,EAAE,EAAE;QACHjG,QAAQ,EAAC/E,gBAAgB,KAAG,MAAM,GAAC,qBAAqB,GAAE4F,gBAAgB,IAAI9D,YAAY,KAAK,KAAK,GAAG,qBAAqB,GAAG,EAAE;QACjIkN,MAAM,EAAChP,gBAAgB,KAAG,MAAM,GAAC,iBAAiB,GAAE4F,gBAAgB,IAAI9D,YAAY,KAAK,KAAK,GAAG,iBAAiB,GAAG,EAAE;QACvH,gBAAgB,EAAEtB,QAAQ,GAAG,MAAM,GAAG,MAAM;QAC5C,8CAA8C,EAAE;UAC/CwO,MAAM,EAAE,gBAAgB;UACxB;UACA;UACA,GAAGtG,WAAW;UACd,GAAGgF,aAAa;UACEvB,MAAM,EAAE,cAAc;UACxCxG,SAAS,EAAE,GAAG+H,aAAa,CAAC/H,SAAS,aAAa;UAElDyJ,QAAQ,EAAE;QACX;MACD,CAAE;MACFC,iBAAiB,EAAE,IAAK;MAAAxD,QAAA,gBAExBpN,OAAA;QAAKyM,KAAK,EAAE;UAAES,YAAY,EAAE,KAAK;UAAEF,OAAO,EAAE;QAAO,CAAE;QAAAI,QAAA,EACnD,CAAApK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6N,aAAa,kBAC9B7Q,OAAA,CAACN,UAAU;UACV6M,EAAE,EAAE;YACHjG,QAAQ,EAAE,OAAO;YACjBwK,SAAS,EAAE,iCAAiC;YAC5C7J,IAAI,EAAE,MAAM;YACZF,KAAK,EAAE,MAAM;YACb2G,MAAM,EAAE,OAAO;YACf5E,UAAU,EAAE,iBAAiB;YAC7BiI,MAAM,EAAE,gBAAgB;YACxBR,MAAM,EAAE,QAAQ;YAChBrG,YAAY,EAAE,MAAM;YACpB8G,OAAO,EAAE;UACV,CAAE;UAAA5D,QAAA,eAEFpN,OAAA,CAACJ,SAAS;YAAC2M,EAAE,EAAE;cAAE0E,IAAI,EAAE,KAAK;cAAEpJ,KAAK,EAAE;YAAO;UAAE;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MACZ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACN9M,OAAA;QAAKyM,KAAK,EAAE;UAACuE,OAAO,EAAE/N,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEiO,OAAO,GAAG,GAAGjO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiO,OAAO,IAAI,GAAG;QACzF,CAAE;QAAA9D,QAAA,gBACcpN,OAAA,CAACF,gBAAgB;UAE7BqR,GAAG,EAAErD,YAAa;UAClBrB,KAAK,EAAE;YAAEjE,SAAS,EAAE;UAAQ,CAAE;UAC9B4I,OAAO,EAAE;YACRC,eAAe,EAAE,CAAC1D,cAAc;YAChC2D,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAAvE,QAAA,eAGDpN,OAAA,CAACR,GAAG;YACHiN,KAAK,EAAE;cACN;cACA7D,MAAM,EAAEuC;YACS,CAAE;YACFgG,GAAG,EAAEtD,UAAW;YAAAT,QAAA,gBAElCpN,OAAA,CAACR,GAAG;cACHwN,OAAO,EAAC,MAAM;cACd4E,aAAa,EAAC,QAAQ;cACtBC,QAAQ,EAAC,MAAM;cACfC,cAAc,EAAC,QAAQ;cAAA1E,QAAA,EAEtBtK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwK,GAAG,CAAC,CAACyE,SAAc,EAAEC,SAAiB,KACvDD,SAAS,CAAC5L,WAAW,CAACmH,GAAG,CAAC,CAAC2E,SAAc,EAAEC,QAAgB,kBAC1DlS,OAAA,CAACR,GAAG;gBAEH2S,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAEH,SAAS,CAACI,GAAI;gBACnBC,GAAG,EAAEL,SAAS,CAACM,OAAO,IAAI,OAAQ;gBAClChG,EAAE,EAAE;kBACH/D,SAAS,EAAEuJ,SAAS,CAACtJ,cAAc,IAAIwJ,SAAS,CAACxJ,cAAc,IAAI,OAAO;kBAC1EV,SAAS,EAAEgK,SAAS,CAAC/J,SAAS,IAAI,QAAQ;kBAC1CU,SAAS,EAAEuJ,SAAS,CAAC7L,GAAG,IAAI,SAAS;kBACrC0C,UAAU,EAAEmJ,SAAS,CAAClJ,eAAe,IAAI,SAAS;kBAClDJ,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,GAAGqJ,SAAS,CAACpJ,aAAa,IAAI,GAAG,IAAI;kBAC7C;kBACA6E,MAAM,EAAE,CAAC;kBACTsD,OAAO,EAAE,CAAC;kBACV9G,YAAY,EAAE;gBACf,CAAE;gBACFsI,OAAO,EAAEA,CAAA,KAAM;kBACd,IAAIT,SAAS,CAACU,SAAS,EAAE;oBACxB,MAAMvE,SAAS,GAAG6D,SAAS,CAACU,SAAS;oBACrCpE,MAAM,CAACG,IAAI,CAACN,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;kBACxD;gBACD,CAAE;gBACFzB,KAAK,EAAE;kBAAEiG,MAAM,EAAEX,SAAS,CAACU,SAAS,GAAG,SAAS,GAAG;gBAAU;cAAE,GAtB1D,GAAGV,SAAS,CAACY,EAAE,IAAIT,QAAQ,EAAE;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBlC,CACD,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EAGLjK,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEyK,GAAG,CACxB,CAACsF,SAAc,EAAEpF,KAAU;cAAA,IAAAqF,qBAAA;cAAA,OAC1BD,SAAS,CAACE,IAAI,iBACb9S,OAAA,CAACT,UAAU;gBACkB;gBAC5BkR,SAAS,EAAC,eAAe;gBACzBlE,EAAE,EAAE;kBACHwG,UAAU,EAAE,UAAU;kBACtBC,SAAS,EAAE,YAAY;kBACvBjL,SAAS;kBACTkL,SAAS,EAAE,CAAC;kBACZpL,KAAK,EAAE,EAAAgL,qBAAA,GAAAD,SAAS,CAACnL,cAAc,cAAAoL,qBAAA,uBAAxBA,qBAAA,CAA0B/K,SAAS,KAAIP,SAAS,CAACM,KAAK;kBAC7DmJ,OAAO,EAAC;gBACT,CAAE;gBACFkC,uBAAuB,EAAElK,iBAAiB,CAAC4J,SAAS,CAACE,IAAI,CAAE,CAAC;cAAA,GAVvDF,SAAS,CAACD,EAAE,IAAInF,KAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAW1B,CACD;YAAA,CACH,CAAC,EAEAqG,MAAM,CAACC,IAAI,CAAC7J,cAAc,CAAC,CAAC+D,GAAG,CAAExD,WAAW;cAAA,IAAAuJ,qBAAA,EAAAC,sBAAA;cAAA,oBAC5CtT,OAAA,CAACR,GAAG;gBAEH+M,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACf8E,cAAc,EAAE/C,YAAY,EAAAsE,qBAAA,GAAC9J,cAAc,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAuJ,qBAAA,uBAA9BA,qBAAA,CAAgCrL,SAAS,CAAC;kBACvE6J,QAAQ,EAAE,MAAM;kBAChBnE,MAAM,EAAE,CAAC;kBACTjD,eAAe,GAAA6I,sBAAA,GAAE/J,cAAc,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAwJ,sBAAA,uBAA9BA,sBAAA,CAAgCvK,eAAe;kBAC9DiI,OAAO,EAAE;gBACZ,CAAE;gBAAA5D,QAAA,EAED7D,cAAc,CAACO,WAAW,CAAC,CAACwD,GAAG,CAAC,CAACzD,MAAW,EAAE2D,KAAa;kBAAA,IAAA+F,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBAAA,oBAC3D5T,OAAA,CAACV,MAAM;oBAENkT,OAAO,EAAEA,CAAA,KAAMzE,kBAAkB,CAAClE,MAAM,CAACgK,YAAY,CAAE;oBACvDxH,OAAO,EAAC,WAAW;oBACnBE,EAAE,EAAE;sBACHuH,WAAW,EAAE,MAAM;sBACnBpG,MAAM,EAAE,OAAO;sBACfjD,eAAe,EAAE,EAAA8I,qBAAA,GAAA1J,MAAM,CAACkK,gBAAgB,cAAAR,qBAAA,uBAAvBA,qBAAA,CAAyBS,qBAAqB,KAAI,SAAS;sBAC5EnM,KAAK,EAAE,EAAA2L,sBAAA,GAAA3J,MAAM,CAACkK,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBS,eAAe,KAAI,MAAM;sBACzDlD,MAAM,EAAE,cAAA0C,sBAAA,GAAa5J,MAAM,CAACkK,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBS,iBAAiB,EAAE,IAAI,aAAa;sBAClFC,QAAQ,EAAE,EAAAT,sBAAA,GAAA7J,MAAM,CAACkK,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyBU,QAAQ,KAAI,MAAM;sBACrDzL,KAAK,EAAE,EAAAgL,sBAAA,GAAA9J,MAAM,CAACkK,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyB5I,KAAK,KAAI,MAAM;sBAC/C;sBACA;sBACA;sBACA;sBACAsJ,aAAa,EAAE,MAAM;sBACrBnK,YAAY,EAAE,EAAA0J,sBAAA,GAAA/J,MAAM,CAACkK,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBU,YAAY,KAAI,KAAK;sBAC5DtD,OAAO,EAAE,kCAAkC;sBACVuD,UAAU,EAAE,qCAAqC;sBACjDzD,SAAS,EAAE;oBAC7C,CAAE;oBAAA1D,QAAA,EAEDvD,MAAM,CAAC2K;kBAAU,GAtBbhH,KAAK;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuBH,CAAC;gBAAA,CACT;cAAC,GApCGhD,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCZ,CAAC;YAAA,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAvHF,aAAaa,cAAc,EAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyHL,CAAC,eAClB9M,OAAA;UAAAoN,QAAA,EAES1K,UAAU,IAAI,CAAC,IAAI2I,cAAc,gBAAGrL,OAAA,CAAAE,SAAA;YAAAkN,QAAA,EAAGhB,cAAc,CAAC;UAAC,gBAAG,CAAC,GAAG;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAER,CAAC;AAACvJ,EAAA,CAlqBIjC,iBAAuC;EAAA,QA4CxCzB,cAAc;AAAA;AAAA4U,EAAA,GA5CbnT,iBAAuC;AAoqB7C,eAAeA,iBAAiB;AAAC,IAAAmT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}